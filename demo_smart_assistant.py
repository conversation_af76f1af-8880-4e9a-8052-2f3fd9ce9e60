"""
智能决策助手演示程序
展示完整的意图识别和智能体调用流程
"""

import re
from typing import Dict, Any, Optional, Tuple

class MockAgent:
    """模拟智能体类"""
    def __init__(self, name: str, description: str):
        self.name = name
        self.description = description
        self.instructions = f"我是{name}，专门负责{description}"
    
    def run(self, message: str, **kwargs):
        """模拟智能体运行"""
        class MockResponse:
            def __init__(self, content):
                self.content = content
        
        return MockResponse(f"[{self.name}] 正在处理您的请求：{message}\n\n这是一个模拟回复，展示{self.description}的处理结果。")

class MockMultiAgentManager:
    """模拟多智能体管理器"""
    
    def __init__(self):
        self.agents = {
            "composition_agent": MockAgent("作文助手", "智能写作助手，擅长根据用户需求生成高质量、结构清晰、语言生动的文章"),
            "daily_report_agent": MockAgent("日报助手", "企业文档撰写专家，擅长简洁记录和展示工作进展"),
            "week_report_agent": MockAgent("周报助手", "专业周报撰写助手，帮助整理和总结一周工作内容"),
            "month_report_agent": MockAgent("月报助手", "专业月报撰写助手，帮助整理和总结月度工作内容"),
            "official_document_writing_agent": MockAgent("公文写作助手", "专业公文写作助手，擅长各类正式文档的撰写"),
            "new_media_copywriting_agent": MockAgent("新媒体文案助手", "新媒体文案创作专家，擅长各种平台的内容创作"),
            "xiaohongshu_copywriting_agent": MockAgent("小红书文案助手", "小红书平台文案创作专家，熟悉平台特色和用户偏好"),
            "xiaohongshu_creation_agent": MockAgent("小红书创作助手", "小红书内容创作助手，提供全方位的创作支持"),
            "my_new_agent": MockAgent("通用助手", "自定义智能体助手，可以根据需求进行个性化配置")
        }
        self.conversation_history = {}
    
    def get_agent(self, agent_id: str):
        return self.agents.get(agent_id)
    
    def get_available_agents(self):
        return {
            agent_id: {
                "name": agent.name,
                "description": agent.description
            }
            for agent_id, agent in self.agents.items()
        }
    
    def chat_with_agent_stream(self, agent_id: str, message: str, session_id: str = "default", **kwargs):
        """模拟流式对话"""
        agent = self.get_agent(agent_id)
        if not agent:
            yield {"type": "error", "content": f"错误: 无法找到智能体 {agent_id}"}
            return
        
        yield {"type": "reasoning", "content": f"我是{agent.name}，让我分析您的请求"}
        yield {"type": "reasoning", "content": "正在生成内容，我会确保回复符合您的需求"}
        
        response = agent.run(message, **kwargs)
        yield {"type": "reasoning", "content": "内容生成完成，如果您需要修改或完善，请告诉我具体的要求"}
        yield {"type": "content", "content": response.content}

class DemoIntentDecisionAgent:
    """演示版意图决策智能体"""
    
    def __init__(self):
        self.manager = MockMultiAgentManager()
    
    def analyze_intent(self, user_input: str) -> Tuple[str, str]:
        """分析用户意图，返回(智能体ID, 选择理由)"""
        return self._simple_intent_matching(user_input)
    
    def _simple_intent_matching(self, user_input: str) -> Tuple[str, str]:
        """简单的规则匹配"""
        user_input_lower = user_input.lower()
        
        # 定义关键词匹配规则
        rules = [
            (["作文", "写作", "文章", "散文", "记叙文", "议论文", "写一篇"], "composition_agent", "检测到写作相关关键词"),
            (["日报", "今日工作", "当日总结", "今天的工作"], "daily_report_agent", "检测到日报相关关键词"),
            (["周报", "本周工作", "一周总结", "这周的工作"], "week_report_agent", "检测到周报相关关键词"),
            (["月报", "月度总结", "本月工作", "这个月的工作"], "month_report_agent", "检测到月报相关关键词"),
            (["公文", "通知", "公告", "函件", "正式文档"], "official_document_writing_agent", "检测到公文相关关键词"),
            (["小红书文案"], "xiaohongshu_copywriting_agent", "检测到小红书文案关键词"),
            (["小红书创作", "小红书内容"], "xiaohongshu_creation_agent", "检测到小红书创作关键词"),
            (["文案", "广告", "营销", "推广", "微博", "朋友圈"], "new_media_copywriting_agent", "检测到新媒体文案关键词"),
        ]
        
        # 匹配关键词
        for keywords, agent_id, reason in rules:
            if any(keyword in user_input for keyword in keywords):
                return agent_id, reason
        
        # 默认使用通用智能体
        return "my_new_agent", "未匹配到特定类型，使用通用智能体"
    
    def process_user_request(self, user_input: str, session_id: str = "default"):
        """处理用户请求，自动选择智能体并返回流式响应"""
        # 分析用户意图
        agent_id, reason = self.analyze_intent(user_input)
        
        # 获取智能体信息
        available_agents = self.manager.get_available_agents()
        agent_info = available_agents.get(agent_id, {})
        agent_name = agent_info.get("name", agent_id)
        
        # 生成决策信息
        yield {"type": "decision", "content": f"🤖 意图分析：{reason}"}
        yield {"type": "decision", "content": f"📋 选择智能体：{agent_name}"}
        yield {"type": "decision", "content": f"🚀 开始处理您的请求..."}
        
        # 调用选定的智能体
        try:
            for response in self.manager.chat_with_agent_stream(agent_id, user_input, session_id):
                yield response
        except Exception as e:
            yield {"type": "error", "content": f"调用智能体时出错: {str(e)}"}

class DemoSmartAssistant:
    """演示版智能助手"""
    
    def __init__(self):
        self.decision_agent = DemoIntentDecisionAgent()
        self.session_id = "demo_session"
        print("🤖 智能助手演示程序已启动！")
        print("💡 我会自动分析您的需求并选择最合适的智能体来帮助您")
        print("📝 支持的功能包括：作文写作、工作报告、公文写作、新媒体文案、小红书内容等")
        print("🔄 输入 'quit' 或 'exit' 退出程序\n")
    
    def run_demo(self):
        """运行演示"""
        # 预设的演示用例
        demo_cases = [
            "请帮我写一篇关于春天的作文，500字左右",
            "我需要写今天的工作日报",
            "帮我整理一下本周的工作周报",
            "需要写一个月度工作总结",
            "帮我写一个会议通知",
            "我要写一个产品推广文案",
            "帮我写小红书的种草文案",
            "我想创作小红书的内容",
            "帮我分析一下这个问题"
        ]
        
        print("=== 自动演示模式 ===\n")
        
        for i, user_input in enumerate(demo_cases, 1):
            print(f"演示 {i}/9: {user_input}")
            print("-" * 50)
            
            # 处理用户请求
            for response in self.decision_agent.process_user_request(user_input, self.session_id):
                if response["type"] == "decision":
                    print(f"🧠 {response['content']}")
                elif response["type"] == "reasoning":
                    print(f"💭 {response['content']}")
                elif response["type"] == "content":
                    print(f"\n📄 生成内容：\n{response['content']}")
                elif response["type"] == "error":
                    print(f"❌ {response['content']}")
            
            print("\n" + "="*50 + "\n")
            
            # 暂停一下，让用户看清楚
            input("按回车键继续下一个演示...")
            print()
    
    def run_interactive(self):
        """运行交互式演示"""
        print("=== 交互式演示模式 ===\n")
        
        while True:
            try:
                # 获取用户输入
                user_input = input("👤 请输入您的需求 (输入 'demo' 查看自动演示): ").strip()
                
                # 检查退出命令
                if user_input.lower() in ['quit', 'exit', '退出', '结束']:
                    print("👋 再见！感谢使用智能助手演示程序！")
                    break
                
                # 检查演示命令
                if user_input.lower() == 'demo':
                    self.run_demo()
                    continue
                
                if not user_input:
                    print("❌ 请输入有效的内容")
                    continue
                
                print("\n" + "="*50)
                
                # 处理用户请求
                for response in self.decision_agent.process_user_request(user_input, self.session_id):
                    if response["type"] == "decision":
                        print(f"🧠 {response['content']}")
                    elif response["type"] == "reasoning":
                        print(f"💭 {response['content']}")
                    elif response["type"] == "content":
                        print(f"\n📄 生成内容：\n{response['content']}")
                    elif response["type"] == "error":
                        print(f"❌ {response['content']}")
                
                print("\n" + "="*50 + "\n")
                
            except KeyboardInterrupt:
                print("\n👋 程序被用户中断，再见！")
                break
            except Exception as e:
                print(f"❌ 程序出错: {e}")

def main():
    """主函数"""
    assistant = DemoSmartAssistant()
    
    print("选择运行模式：")
    print("1. 自动演示模式 - 展示预设的测试用例")
    print("2. 交互式模式 - 手动输入测试")
    
    while True:
        choice = input("\n请选择模式 (1/2): ").strip()
        if choice == "1":
            assistant.run_demo()
            break
        elif choice == "2":
            assistant.run_interactive()
            break
        else:
            print("❌ 请输入 1 或 2")

def generate_html_demo():
    """生成HTML演示文件"""
    html_content = '''<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能决策助手演示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', 'Segoe UI', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 0;
            min-height: 600px;
        }

        .sidebar {
            background: #f8f9fa;
            padding: 30px;
            border-right: 1px solid #e9ecef;
        }

        .sidebar h3 {
            color: #495057;
            margin-bottom: 20px;
            font-size: 1.3em;
        }

        .agent-list {
            list-style: none;
        }

        .agent-item {
            background: white;
            margin-bottom: 15px;
            padding: 15px;
            border-radius: 10px;
            border-left: 4px solid #667eea;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: transform 0.2s ease;
        }

        .agent-item:hover {
            transform: translateX(5px);
        }

        .agent-name {
            font-weight: bold;
            color: #495057;
            margin-bottom: 5px;
        }

        .agent-desc {
            font-size: 0.9em;
            color: #6c757d;
            line-height: 1.4;
        }

        .demo-area {
            padding: 30px;
        }

        .demo-section {
            margin-bottom: 30px;
        }

        .demo-section h3 {
            color: #495057;
            margin-bottom: 15px;
            font-size: 1.3em;
        }

        .test-cases {
            display: grid;
            gap: 15px;
        }

        .test-case {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            border: 1px solid #e9ecef;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .test-case:hover {
            background: #e3f2fd;
            border-color: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.2);
        }

        .test-input {
            font-weight: bold;
            color: #495057;
            margin-bottom: 10px;
        }

        .test-result {
            display: none;
            margin-top: 15px;
            padding: 15px;
            background: white;
            border-radius: 8px;
            border-left: 4px solid #28a745;
        }

        .test-result.show {
            display: block;
            animation: slideDown 0.3s ease;
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .decision-info {
            background: #fff3cd;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 10px;
            border-left: 4px solid #ffc107;
        }

        .agent-response {
            background: #d1ecf1;
            padding: 10px;
            border-radius: 5px;
            border-left: 4px solid #17a2b8;
        }

        .input-demo {
            background: white;
            padding: 20px;
            border-radius: 10px;
            border: 1px solid #e9ecef;
            margin-top: 20px;
        }

        .input-group {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
        }

        #userInput {
            flex: 1;
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }

        #userInput:focus {
            outline: none;
            border-color: #667eea;
        }

        #analyzeBtn {
            padding: 12px 24px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: bold;
            transition: transform 0.2s ease;
        }

        #analyzeBtn:hover {
            transform: translateY(-2px);
        }

        #result {
            margin-top: 15px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #e9ecef;
            display: none;
        }

        .footer {
            background: #495057;
            color: white;
            text-align: center;
            padding: 20px;
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }

            .sidebar {
                border-right: none;
                border-bottom: 1px solid #e9ecef;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 智能决策助手</h1>
            <p>自动分析用户意图，智能选择专业助手</p>
        </div>

        <div class="main-content">
            <div class="sidebar">
                <h3>🎯 可用智能体</h3>
                <ul class="agent-list">
                    <li class="agent-item">
                        <div class="agent-name">📝 作文助手</div>
                        <div class="agent-desc">智能写作助手，擅长各类文章创作</div>
                    </li>
                    <li class="agent-item">
                        <div class="agent-name">📊 日报助手</div>
                        <div class="agent-desc">专业日报撰写，简洁记录工作进展</div>
                    </li>
                    <li class="agent-item">
                        <div class="agent-name">📈 周报助手</div>
                        <div class="agent-desc">周度工作总结，整理一周工作内容</div>
                    </li>
                    <li class="agent-item">
                        <div class="agent-name">📋 月报助手</div>
                        <div class="agent-desc">月度工作汇报，总结阶段性成果</div>
                    </li>
                    <li class="agent-item">
                        <div class="agent-name">📄 公文写作助手</div>
                        <div class="agent-desc">正式文档撰写，通知公告函件</div>
                    </li>
                    <li class="agent-item">
                        <div class="agent-name">📱 新媒体文案助手</div>
                        <div class="agent-desc">社交媒体文案，广告营销内容</div>
                    </li>
                    <li class="agent-item">
                        <div class="agent-name">🌸 小红书文案助手</div>
                        <div class="agent-desc">小红书平台专用文案创作</div>
                    </li>
                    <li class="agent-item">
                        <div class="agent-name">✨ 小红书创作助手</div>
                        <div class="agent-desc">小红书内容创作全方位支持</div>
                    </li>
                    <li class="agent-item">
                        <div class="agent-name">🔧 通用助手</div>
                        <div class="agent-desc">处理各种自定义需求</div>
                    </li>
                </ul>
            </div>

            <div class="demo-area">
                <div class="demo-section">
                    <h3>🎮 交互式演示</h3>
                    <div class="input-demo">
                        <div class="input-group">
                            <input type="text" id="userInput" placeholder="请输入您的需求，例如：请帮我写一篇关于春天的作文">
                            <button id="analyzeBtn" onclick="analyzeIntent()">分析意图</button>
                        </div>
                        <div id="result"></div>
                    </div>
                </div>

                <div class="demo-section">
                    <h3>📋 预设测试用例</h3>
                    <div class="test-cases">
                        <div class="test-case" onclick="showResult(this, 'composition_agent', '检测到写作相关关键词')">
                            <div class="test-input">📝 请帮我写一篇关于春天的作文，500字左右</div>
                            <div class="test-result">
                                <div class="decision-info">
                                    <strong>🤖 意图分析：</strong>检测到写作相关关键词<br>
                                    <strong>📋 选择智能体：</strong>作文助手
                                </div>
                                <div class="agent-response">
                                    <strong>📄 处理结果：</strong>作文助手将为您创作一篇关于春天的优美作文，包含生动的描写和丰富的情感表达。
                                </div>
                            </div>
                        </div>

                        <div class="test-case" onclick="showResult(this, 'daily_report_agent', '检测到日报相关关键词')">
                            <div class="test-input">📊 我需要写今天的工作日报</div>
                            <div class="test-result">
                                <div class="decision-info">
                                    <strong>🤖 意图分析：</strong>检测到日报相关关键词<br>
                                    <strong>📋 选择智能体：</strong>日报助手
                                </div>
                                <div class="agent-response">
                                    <strong>📄 处理结果：</strong>日报助手将帮您整理今日工作内容，生成简洁明了的工作日报格式。
                                </div>
                            </div>
                        </div>

                        <div class="test-case" onclick="showResult(this, 'week_report_agent', '检测到周报相关关键词')">
                            <div class="test-input">📈 帮我整理一下本周的工作周报</div>
                            <div class="test-result">
                                <div class="decision-info">
                                    <strong>🤖 意图分析：</strong>检测到周报相关关键词<br>
                                    <strong>📋 选择智能体：</strong>周报助手
                                </div>
                                <div class="agent-response">
                                    <strong>📄 处理结果：</strong>周报助手将总结本周工作亮点，整理成专业的周度工作汇报。
                                </div>
                            </div>
                        </div>

                        <div class="test-case" onclick="showResult(this, 'official_document_writing_agent', '检测到公文相关关键词')">
                            <div class="test-input">📄 帮我写一个会议通知</div>
                            <div class="test-result">
                                <div class="decision-info">
                                    <strong>🤖 意图分析：</strong>检测到公文相关关键词<br>
                                    <strong>📋 选择智能体：</strong>公文写作助手
                                </div>
                                <div class="agent-response">
                                    <strong>📄 处理结果：</strong>公文写作助手将按照正式文档格式，撰写规范的会议通知。
                                </div>
                            </div>
                        </div>

                        <div class="test-case" onclick="showResult(this, 'new_media_copywriting_agent', '检测到新媒体文案关键词')">
                            <div class="test-input">📱 我要写一个产品推广文案</div>
                            <div class="test-result">
                                <div class="decision-info">
                                    <strong>🤖 意图分析：</strong>检测到新媒体文案关键词<br>
                                    <strong>📋 选择智能体：</strong>新媒体文案助手
                                </div>
                                <div class="agent-response">
                                    <strong>📄 处理结果：</strong>新媒体文案助手将创作吸引人的推广文案，适合各种社交媒体平台。
                                </div>
                            </div>
                        </div>

                        <div class="test-case" onclick="showResult(this, 'xiaohongshu_copywriting_agent', '检测到小红书文案关键词')">
                            <div class="test-input">🌸 帮我写小红书的种草文案</div>
                            <div class="test-result">
                                <div class="decision-info">
                                    <strong>🤖 意图分析：</strong>检测到小红书文案关键词<br>
                                    <strong>📋 选择智能体：</strong>小红书文案助手
                                </div>
                                <div class="agent-response">
                                    <strong>📄 处理结果：</strong>小红书文案助手将创作符合平台特色的种草文案，包含热门话题和标签。
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="footer">
            <p>🚀 智能决策助手演示 - 让AI自动选择最合适的专业助手</p>
        </div>
    </div>

    <script>
        // 意图分析规则
        const intentRules = [
            {keywords: ["作文", "写作", "文章", "散文", "记叙文", "议论文", "写一篇"], agent: "composition_agent", name: "作文助手", reason: "检测到写作相关关键词"},
            {keywords: ["日报", "今日工作", "当日总结", "今天的工作"], agent: "daily_report_agent", name: "日报助手", reason: "检测到日报相关关键词"},
            {keywords: ["周报", "本周工作", "一周总结", "这周的工作"], agent: "week_report_agent", name: "周报助手", reason: "检测到周报相关关键词"},
            {keywords: ["月报", "月度总结", "本月工作", "这个月的工作"], agent: "month_report_agent", name: "月报助手", reason: "检测到月报相关关键词"},
            {keywords: ["公文", "通知", "公告", "函件", "正式文档"], agent: "official_document_writing_agent", name: "公文写作助手", reason: "检测到公文相关关键词"},
            {keywords: ["小红书文案"], agent: "xiaohongshu_copywriting_agent", name: "小红书文案助手", reason: "检测到小红书文案关键词"},
            {keywords: ["小红书创作", "小红书内容"], agent: "xiaohongshu_creation_agent", name: "小红书创作助手", reason: "检测到小红书创作关键词"},
            {keywords: ["文案", "广告", "营销", "推广", "微博", "朋友圈"], agent: "new_media_copywriting_agent", name: "新媒体文案助手", reason: "检测到新媒体文案关键词"}
        ];

        function analyzeIntent() {
            const input = document.getElementById('userInput').value.trim();
            const resultDiv = document.getElementById('result');

            if (!input) {
                alert('请输入内容');
                return;
            }

            // 分析意图
            let selectedAgent = null;
            for (const rule of intentRules) {
                if (rule.keywords.some(keyword => input.includes(keyword))) {
                    selectedAgent = rule;
                    break;
                }
            }

            if (!selectedAgent) {
                selectedAgent = {
                    agent: "my_new_agent",
                    name: "通用助手",
                    reason: "未匹配到特定类型，使用通用智能体"
                };
            }

            // 显示结果
            resultDiv.innerHTML = `
                <div class="decision-info">
                    <strong>🤖 意图分析：</strong>${selectedAgent.reason}<br>
                    <strong>📋 选择智能体：</strong>${selectedAgent.name}
                </div>
                <div class="agent-response">
                    <strong>📄 处理结果：</strong>${selectedAgent.name}将为您处理这个请求："${input}"
                </div>
            `;
            resultDiv.style.display = 'block';
        }

        function showResult(element, agentId, reason) {
            const result = element.querySelector('.test-result');
            const isVisible = result.classList.contains('show');

            // 隐藏所有其他结果
            document.querySelectorAll('.test-result').forEach(r => r.classList.remove('show'));

            // 切换当前结果的显示状态
            if (!isVisible) {
                result.classList.add('show');
            }
        }

        // 回车键支持
        document.getElementById('userInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                analyzeIntent();
            }
        });
    </script>
</body>
</html>'''

    # 保存HTML文件
    with open('智能决策助手演示.html', 'w', encoding='utf-8') as f:
        f.write(html_content)

    print("✅ HTML演示文件已生成：智能决策助手演示.html")
    print("🌐 请用浏览器打开该文件查看演示效果")

if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1 and sys.argv[1] == '--html':
        generate_html_demo()
    else:
        main()
