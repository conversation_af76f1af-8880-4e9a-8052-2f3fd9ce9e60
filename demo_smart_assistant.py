"""
智能决策助手演示程序
展示完整的意图识别和智能体调用流程
"""

import re
from typing import Dict, Any, Optional, Tuple

class MockAgent:
    """模拟智能体类"""
    def __init__(self, name: str, description: str):
        self.name = name
        self.description = description
        self.instructions = f"我是{name}，专门负责{description}"
    
    def run(self, message: str, **kwargs):
        """模拟智能体运行"""
        class MockResponse:
            def __init__(self, content):
                self.content = content
        
        return MockResponse(f"[{self.name}] 正在处理您的请求：{message}\n\n这是一个模拟回复，展示{self.description}的处理结果。")

class MockMultiAgentManager:
    """模拟多智能体管理器"""
    
    def __init__(self):
        self.agents = {
            "composition_agent": MockAgent("作文助手", "智能写作助手，擅长根据用户需求生成高质量、结构清晰、语言生动的文章"),
            "daily_report_agent": MockAgent("日报助手", "企业文档撰写专家，擅长简洁记录和展示工作进展"),
            "week_report_agent": MockAgent("周报助手", "专业周报撰写助手，帮助整理和总结一周工作内容"),
            "month_report_agent": MockAgent("月报助手", "专业月报撰写助手，帮助整理和总结月度工作内容"),
            "official_document_writing_agent": MockAgent("公文写作助手", "专业公文写作助手，擅长各类正式文档的撰写"),
            "new_media_copywriting_agent": MockAgent("新媒体文案助手", "新媒体文案创作专家，擅长各种平台的内容创作"),
            "xiaohongshu_copywriting_agent": MockAgent("小红书文案助手", "小红书平台文案创作专家，熟悉平台特色和用户偏好"),
            "xiaohongshu_creation_agent": MockAgent("小红书创作助手", "小红书内容创作助手，提供全方位的创作支持"),
            "my_new_agent": MockAgent("通用助手", "自定义智能体助手，可以根据需求进行个性化配置")
        }
        self.conversation_history = {}
    
    def get_agent(self, agent_id: str):
        return self.agents.get(agent_id)
    
    def get_available_agents(self):
        return {
            agent_id: {
                "name": agent.name,
                "description": agent.description
            }
            for agent_id, agent in self.agents.items()
        }
    
    def chat_with_agent_stream(self, agent_id: str, message: str, session_id: str = "default", **kwargs):
        """模拟流式对话"""
        agent = self.get_agent(agent_id)
        if not agent:
            yield {"type": "error", "content": f"错误: 无法找到智能体 {agent_id}"}
            return
        
        yield {"type": "reasoning", "content": f"我是{agent.name}，让我分析您的请求"}
        yield {"type": "reasoning", "content": "正在生成内容，我会确保回复符合您的需求"}
        
        response = agent.run(message, **kwargs)
        yield {"type": "reasoning", "content": "内容生成完成，如果您需要修改或完善，请告诉我具体的要求"}
        yield {"type": "content", "content": response.content}

class DemoIntentDecisionAgent:
    """演示版意图决策智能体"""
    
    def __init__(self):
        self.manager = MockMultiAgentManager()
    
    def analyze_intent(self, user_input: str) -> Tuple[str, str]:
        """分析用户意图，返回(智能体ID, 选择理由)"""
        return self._simple_intent_matching(user_input)
    
    def _simple_intent_matching(self, user_input: str) -> Tuple[str, str]:
        """简单的规则匹配"""
        user_input_lower = user_input.lower()
        
        # 定义关键词匹配规则
        rules = [
            (["作文", "写作", "文章", "散文", "记叙文", "议论文", "写一篇"], "composition_agent", "检测到写作相关关键词"),
            (["日报", "今日工作", "当日总结", "今天的工作"], "daily_report_agent", "检测到日报相关关键词"),
            (["周报", "本周工作", "一周总结", "这周的工作"], "week_report_agent", "检测到周报相关关键词"),
            (["月报", "月度总结", "本月工作", "这个月的工作"], "month_report_agent", "检测到月报相关关键词"),
            (["公文", "通知", "公告", "函件", "正式文档"], "official_document_writing_agent", "检测到公文相关关键词"),
            (["小红书文案"], "xiaohongshu_copywriting_agent", "检测到小红书文案关键词"),
            (["小红书创作", "小红书内容"], "xiaohongshu_creation_agent", "检测到小红书创作关键词"),
            (["文案", "广告", "营销", "推广", "微博", "朋友圈"], "new_media_copywriting_agent", "检测到新媒体文案关键词"),
        ]
        
        # 匹配关键词
        for keywords, agent_id, reason in rules:
            if any(keyword in user_input for keyword in keywords):
                return agent_id, reason
        
        # 默认使用通用智能体
        return "my_new_agent", "未匹配到特定类型，使用通用智能体"
    
    def process_user_request(self, user_input: str, session_id: str = "default"):
        """处理用户请求，自动选择智能体并返回流式响应"""
        # 分析用户意图
        agent_id, reason = self.analyze_intent(user_input)
        
        # 获取智能体信息
        available_agents = self.manager.get_available_agents()
        agent_info = available_agents.get(agent_id, {})
        agent_name = agent_info.get("name", agent_id)
        
        # 生成决策信息
        yield {"type": "decision", "content": f"🤖 意图分析：{reason}"}
        yield {"type": "decision", "content": f"📋 选择智能体：{agent_name}"}
        yield {"type": "decision", "content": f"🚀 开始处理您的请求..."}
        
        # 调用选定的智能体
        try:
            for response in self.manager.chat_with_agent_stream(agent_id, user_input, session_id):
                yield response
        except Exception as e:
            yield {"type": "error", "content": f"调用智能体时出错: {str(e)}"}

class DemoSmartAssistant:
    """演示版智能助手"""
    
    def __init__(self):
        self.decision_agent = DemoIntentDecisionAgent()
        self.session_id = "demo_session"
        print("🤖 智能助手演示程序已启动！")
        print("💡 我会自动分析您的需求并选择最合适的智能体来帮助您")
        print("📝 支持的功能包括：作文写作、工作报告、公文写作、新媒体文案、小红书内容等")
        print("🔄 输入 'quit' 或 'exit' 退出程序\n")
    
    def run_demo(self):
        """运行演示"""
        # 预设的演示用例
        demo_cases = [
            "请帮我写一篇关于春天的作文，500字左右",
            "我需要写今天的工作日报",
            "帮我整理一下本周的工作周报",
            "需要写一个月度工作总结",
            "帮我写一个会议通知",
            "我要写一个产品推广文案",
            "帮我写小红书的种草文案",
            "我想创作小红书的内容",
            "帮我分析一下这个问题"
        ]
        
        print("=== 自动演示模式 ===\n")
        
        for i, user_input in enumerate(demo_cases, 1):
            print(f"演示 {i}/9: {user_input}")
            print("-" * 50)
            
            # 处理用户请求
            for response in self.decision_agent.process_user_request(user_input, self.session_id):
                if response["type"] == "decision":
                    print(f"🧠 {response['content']}")
                elif response["type"] == "reasoning":
                    print(f"💭 {response['content']}")
                elif response["type"] == "content":
                    print(f"\n📄 生成内容：\n{response['content']}")
                elif response["type"] == "error":
                    print(f"❌ {response['content']}")
            
            print("\n" + "="*50 + "\n")
            
            # 暂停一下，让用户看清楚
            input("按回车键继续下一个演示...")
            print()
    
    def run_interactive(self):
        """运行交互式演示"""
        print("=== 交互式演示模式 ===\n")
        
        while True:
            try:
                # 获取用户输入
                user_input = input("👤 请输入您的需求 (输入 'demo' 查看自动演示): ").strip()
                
                # 检查退出命令
                if user_input.lower() in ['quit', 'exit', '退出', '结束']:
                    print("👋 再见！感谢使用智能助手演示程序！")
                    break
                
                # 检查演示命令
                if user_input.lower() == 'demo':
                    self.run_demo()
                    continue
                
                if not user_input:
                    print("❌ 请输入有效的内容")
                    continue
                
                print("\n" + "="*50)
                
                # 处理用户请求
                for response in self.decision_agent.process_user_request(user_input, self.session_id):
                    if response["type"] == "decision":
                        print(f"🧠 {response['content']}")
                    elif response["type"] == "reasoning":
                        print(f"💭 {response['content']}")
                    elif response["type"] == "content":
                        print(f"\n📄 生成内容：\n{response['content']}")
                    elif response["type"] == "error":
                        print(f"❌ {response['content']}")
                
                print("\n" + "="*50 + "\n")
                
            except KeyboardInterrupt:
                print("\n👋 程序被用户中断，再见！")
                break
            except Exception as e:
                print(f"❌ 程序出错: {e}")

def main():
    """主函数"""
    assistant = DemoSmartAssistant()
    
    print("选择运行模式：")
    print("1. 自动演示模式 - 展示预设的测试用例")
    print("2. 交互式模式 - 手动输入测试")
    
    while True:
        choice = input("\n请选择模式 (1/2): ").strip()
        if choice == "1":
            assistant.run_demo()
            break
        elif choice == "2":
            assistant.run_interactive()
            break
        else:
            print("❌ 请输入 1 或 2")

if __name__ == "__main__":
    main()
