"""
智能助手主程序
用户只需要输入内容，系统自动判断意图并调用相应的智能体
"""

from intent_decision_agent import process_user_request, process_user_request_simple
import sys

class SmartAssistant:
    """智能助手"""
    
    def __init__(self):
        self.session_id = "main_session"
        print("🤖 智能助手已启动！")
        print("💡 我会自动分析您的需求并选择最合适的智能体来帮助您")
        print("📝 支持的功能包括：作文写作、工作报告、公文写作、新媒体文案、小红书内容等")
        print("🔄 输入 'quit' 或 'exit' 退出程序\n")
    
    def run_interactive(self):
        """运行交互式对话"""
        while True:
            try:
                # 获取用户输入
                user_input = input("👤 请输入您的需求: ").strip()
                
                # 检查退出命令
                if user_input.lower() in ['quit', 'exit', '退出', '结束']:
                    print("👋 再见！感谢使用智能助手！")
                    break
                
                if not user_input:
                    print("❌ 请输入有效的内容")
                    continue
                
                print("\n" + "="*50)
                
                # 处理用户请求（流式输出）
                for response in process_user_request(user_input, self.session_id):
                    if response["type"] == "decision":
                        print(f"🧠 {response['content']}")
                    elif response["type"] == "reasoning":
                        print(f"💭 {response['content']}")
                    elif response["type"] == "content":
                        print(f"\n📄 生成内容：\n{response['content']}")
                    elif response["type"] == "error":
                        print(f"❌ {response['content']}")
                
                print("\n" + "="*50 + "\n")
                
            except KeyboardInterrupt:
                print("\n👋 程序被用户中断，再见！")
                break
            except Exception as e:
                print(f"❌ 程序出错: {e}")
    
    def process_single_request(self, user_input: str) -> str:
        """处理单个请求（非交互式）"""
        return process_user_request_simple(user_input, self.session_id)


def main():
    """主函数"""
    assistant = SmartAssistant()
    
    # 检查命令行参数
    if len(sys.argv) > 1:
        # 如果有命令行参数，处理单个请求
        user_input = " ".join(sys.argv[1:])
        print(f"👤 用户请求: {user_input}\n")
        
        result = assistant.process_single_request(user_input)
        print(result)
    else:
        # 否则运行交互式模式
        assistant.run_interactive()


if __name__ == "__main__":
    main()
