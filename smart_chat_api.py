"""
智能决策助手API
为HTML界面提供后端支持
"""

from flask import Flask, request, jsonify, Response
from flask_cors import CORS
from intent_decision_agent import process_user_request
import json
import uuid
from datetime import datetime

app = Flask(__name__)
CORS(app)  # 允许跨域请求

# 存储会话信息
sessions = {}

@app.route('/')
def index():
    """主页 - 返回HTML文件"""
    try:
        with open('multi_agent_chat.html', 'r', encoding='utf-8') as f:
            return f.read()
    except FileNotFoundError:
        return "HTML文件未找到", 404

@app.route('/api/smart_chat', methods=['POST'])
def smart_chat():
    """智能决策助手聊天API - 流式响应"""
    try:
        data = request.get_json()
        user_input = data.get('message', '').strip()
        session_id = data.get('session_id', str(uuid.uuid4()))
        
        if not user_input:
            return jsonify({'error': '请输入有效的内容'}), 400
        
        # 记录会话信息
        if session_id not in sessions:
            sessions[session_id] = {
                'created_at': datetime.now(),
                'messages': []
            }
        
        sessions[session_id]['messages'].append({
            'role': 'user',
            'content': user_input,
            'timestamp': datetime.now()
        })
        
        def generate():
            try:
                for response in process_user_request(user_input, session_id):
                    yield f"data: {json.dumps(response, ensure_ascii=False)}\n\n"
                yield f"data: {json.dumps({'type': 'end'}, ensure_ascii=False)}\n\n"
            except Exception as e:
                yield f"data: {json.dumps({'type': 'error', 'content': str(e)}, ensure_ascii=False)}\n\n"
        
        return Response(generate(), mimetype='text/plain')
        
    except Exception as e:
        return jsonify({'error': f'处理请求时出错: {str(e)}'}), 500

@app.route('/api/chat', methods=['POST'])
def simple_chat():
    """简单聊天API - 非流式响应"""
    try:
        data = request.get_json()
        user_input = data.get('message', '').strip()
        session_id = data.get('session_id', str(uuid.uuid4()))
        
        if not user_input:
            return jsonify({'error': '请输入有效的内容'}), 400
        
        # 记录会话信息
        if session_id not in sessions:
            sessions[session_id] = {
                'created_at': datetime.now(),
                'messages': []
            }
        
        sessions[session_id]['messages'].append({
            'role': 'user',
            'content': user_input,
            'timestamp': datetime.now()
        })
        
        # 收集所有响应
        responses = []
        for response in process_user_request(user_input, session_id):
            responses.append(response)
        
        # 找到最终内容
        final_content = ""
        decision_info = []
        
        for resp in responses:
            if resp.get('type') == 'decision':
                decision_info.append(resp.get('content', ''))
            elif resp.get('type') == 'content':
                final_content = resp.get('content', '')
        
        # 构建完整响应
        full_response = ""
        if decision_info:
            full_response += "\n".join(decision_info) + "\n\n"
        full_response += final_content
        
        sessions[session_id]['messages'].append({
            'role': 'assistant',
            'content': full_response,
            'timestamp': datetime.now()
        })
        
        return jsonify({
            'response': full_response,
            'session_id': session_id,
            'decision_info': decision_info
        })
        
    except Exception as e:
        return jsonify({'error': f'处理请求时出错: {str(e)}'}), 500

@app.route('/api/sessions')
def get_sessions():
    """获取所有会话"""
    session_list = []
    for session_id, session_data in sessions.items():
        session_list.append({
            'session_id': session_id,
            'created_at': session_data['created_at'].isoformat(),
            'message_count': len(session_data['messages'])
        })
    return jsonify(session_list)

@app.route('/api/sessions/<session_id>')
def get_session(session_id):
    """获取特定会话的详细信息"""
    if session_id not in sessions:
        return jsonify({'error': '会话不存在'}), 404
    
    session_data = sessions[session_id]
    return jsonify({
        'session_id': session_id,
        'created_at': session_data['created_at'].isoformat(),
        'messages': [
            {
                'role': msg['role'],
                'content': msg['content'],
                'timestamp': msg['timestamp'].isoformat()
            }
            for msg in session_data['messages']
        ]
    })

@app.route('/api/health')
def health_check():
    """健康检查"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'sessions_count': len(sessions)
    })

# 错误处理
@app.errorhandler(404)
def not_found(error):
    return jsonify({'error': '接口不存在'}), 404

@app.errorhandler(500)
def internal_error(error):
    return jsonify({'error': '服务器内部错误'}), 500

if __name__ == '__main__':
    print("🚀 启动智能决策助手API服务...")
    print("📱 访问地址: http://localhost:5000")
    print("🔄 按 Ctrl+C 停止服务")
    print("\n可用接口:")
    print("  GET  /                    - 主页面")
    print("  POST /api/smart_chat     - 智能决策聊天(流式)")
    print("  POST /api/chat           - 简单聊天(非流式)")
    print("  GET  /api/sessions       - 获取会话列表")
    print("  GET  /api/health         - 健康检查")
    
    app.run(debug=True, host='0.0.0.0', port=5000)
