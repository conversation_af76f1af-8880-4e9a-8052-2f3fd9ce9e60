"""
智能决策助手API
为HTML界面提供后端支持
"""

from flask import Flask, request, jsonify, Response
from flask_cors import CORS
import json
import uuid
from datetime import datetime
import re
from typing import Tuple

# 尝试导入智能决策模块，如果失败则使用离线模式
try:
    from intent_decision_agent import process_user_request
    ONLINE_MODE = True
    print("✅ 智能决策模块加载成功")
except ImportError as e:
    print(f"⚠️  智能决策模块加载失败: {e}")
    print("🔄 启用离线模式...")
    ONLINE_MODE = False

app = Flask(__name__)
CORS(app)  # 允许跨域请求

# 存储会话信息
sessions = {}

# 离线模式的意图分析函数
def offline_intent_analysis(user_input: str) -> Tuple[str, str]:
    """离线模式的简单意图匹配"""
    user_input_lower = user_input.lower()

    # 定义关键词匹配规则
    rules = [
        (["作文", "写作", "文章", "散文", "记叙文", "议论文", "写一篇"], "作文助手", "检测到写作相关关键词"),
        (["日报", "今日工作", "当日总结", "今天的工作"], "日报助手", "检测到日报相关关键词"),
        (["周报", "本周工作", "一周总结", "这周的工作"], "周报助手", "检测到周报相关关键词"),
        (["月报", "月度总结", "本月工作", "这个月的工作"], "月报助手", "检测到月报相关关键词"),
        (["公文", "通知", "公告", "函件", "正式文档"], "公文写作助手", "检测到公文相关关键词"),
        (["小红书文案"], "小红书文案助手", "检测到小红书文案关键词"),
        (["小红书创作", "小红书内容"], "小红书创作助手", "检测到小红书创作关键词"),
        (["文案", "广告", "营销", "推广", "微博", "朋友圈"], "新媒体文案助手", "检测到新媒体文案关键词"),
    ]

    # 匹配关键词
    for keywords, agent_name, reason in rules:
        if any(keyword in user_input for keyword in keywords):
            return agent_name, reason

    # 默认使用通用助手
    return "通用助手", "未匹配到特定类型，使用通用智能体"

def offline_process_request(user_input: str, session_id: str):
    """离线模式处理用户请求"""
    # 分析意图
    agent_name, reason = offline_intent_analysis(user_input)

    # 生成决策信息
    yield {"type": "decision", "content": f"🤖 意图分析：{reason}"}
    yield {"type": "decision", "content": f"📋 选择智能体：{agent_name}"}
    yield {"type": "decision", "content": f"🚀 开始处理您的请求..."}

    # 模拟推理过程
    yield {"type": "reasoning", "content": f"我是{agent_name}，正在分析您的请求"}
    yield {"type": "reasoning", "content": "正在生成内容，确保回复符合您的需求"}

    # 生成模拟回复
    if "作文" in user_input or "写作" in user_input:
        content = f"""# 关于您的写作请求

我是{agent_name}，很高兴为您服务！

根据您的需求："{user_input}"

我建议您可以从以下几个方面来构思：

## 写作思路
1. **确定主题** - 明确文章的中心思想
2. **构建结构** - 安排好开头、正文、结尾
3. **丰富内容** - 添加具体的例子和细节
4. **润色语言** - 使用生动的词汇和句式

## 注意事项
- 保持逻辑清晰
- 语言表达准确
- 内容充实有趣

如果您需要更具体的帮助，请告诉我更多细节！"""

    elif "日报" in user_input or "工作" in user_input:
        content = f"""# 工作日报模板

我是{agent_name}，为您提供专业的日报格式：

## 📅 工作日报 - {datetime.now().strftime('%Y年%m月%d日')}

### 今日完成工作
1.
2.
3.

### 遇到的问题
-

### 明日工作计划
1.
2.
3.

### 其他说明
-

请根据您的实际工作情况填写具体内容。"""

    elif "文案" in user_input or "推广" in user_input:
        content = f"""# 文案创作建议

我是{agent_name}，为您提供文案创作指导：

## 🎯 文案要素
1. **吸引眼球的标题**
2. **明确的价值主张**
3. **情感共鸣的内容**
4. **清晰的行动指引**

## 📝 创作步骤
1. 分析目标受众
2. 确定核心卖点
3. 选择合适的语调
4. 添加互动元素

## 💡 实用技巧
- 使用数字和具体数据
- 创造紧迫感
- 提供社会证明
- 简洁有力的表达

需要我为您的具体产品或服务定制文案吗？"""

    else:
        content = f"""# 智能助手回复

我是{agent_name}，感谢您的咨询！

您的请求："{user_input}"

## 🤖 处理说明
目前系统运行在离线模式下，我会尽力为您提供帮助。

## 💡 建议
1. 请提供更多具体信息
2. 明确您的具体需求
3. 告诉我期望的输出格式

## 🔧 功能说明
- 支持写作指导
- 工作报告模板
- 文案创作建议
- 通用问题解答

如有其他需要，请随时告诉我！"""

    yield {"type": "reasoning", "content": "内容生成完成，如果您需要修改或完善，请告诉我具体的要求"}
    yield {"type": "content", "content": content}

@app.route('/')
def index():
    """主页 - 返回HTML文件"""
    try:
        with open('multi_agent_chat.html', 'r', encoding='utf-8') as f:
            return f.read()
    except FileNotFoundError:
        return "HTML文件未找到", 404

@app.route('/api/smart_chat', methods=['POST'])
def smart_chat():
    """智能决策助手聊天API - 流式响应"""
    try:
        data = request.get_json()
        user_input = data.get('message', '').strip()
        session_id = data.get('session_id', str(uuid.uuid4()))
        
        if not user_input:
            return jsonify({'error': '请输入有效的内容'}), 400
        
        # 记录会话信息
        if session_id not in sessions:
            sessions[session_id] = {
                'created_at': datetime.now(),
                'messages': []
            }
        
        sessions[session_id]['messages'].append({
            'role': 'user',
            'content': user_input,
            'timestamp': datetime.now()
        })
        
        def generate():
            try:
                if ONLINE_MODE:
                    # 在线模式：使用智能决策助手
                    for response in process_user_request(user_input, session_id):
                        yield f"data: {json.dumps(response, ensure_ascii=False)}\n\n"
                else:
                    # 离线模式：使用本地处理
                    for response in offline_process_request(user_input, session_id):
                        yield f"data: {json.dumps(response, ensure_ascii=False)}\n\n"

                yield f"data: {json.dumps({'type': 'end'}, ensure_ascii=False)}\n\n"
            except Exception as e:
                error_msg = f"处理请求时出错: {str(e)}"
                if not ONLINE_MODE:
                    error_msg += " (当前为离线模式)"
                yield f"data: {json.dumps({'type': 'error', 'content': error_msg}, ensure_ascii=False)}\n\n"
        
        return Response(generate(), mimetype='text/plain')
        
    except Exception as e:
        return jsonify({'error': f'处理请求时出错: {str(e)}'}), 500

@app.route('/api/chat', methods=['POST'])
def simple_chat():
    """简单聊天API - 非流式响应"""
    try:
        data = request.get_json()
        user_input = data.get('message', '').strip()
        session_id = data.get('session_id', str(uuid.uuid4()))
        
        if not user_input:
            return jsonify({'error': '请输入有效的内容'}), 400
        
        # 记录会话信息
        if session_id not in sessions:
            sessions[session_id] = {
                'created_at': datetime.now(),
                'messages': []
            }
        
        sessions[session_id]['messages'].append({
            'role': 'user',
            'content': user_input,
            'timestamp': datetime.now()
        })
        
        # 收集所有响应
        responses = []
        try:
            if ONLINE_MODE:
                for response in process_user_request(user_input, session_id):
                    responses.append(response)
            else:
                for response in offline_process_request(user_input, session_id):
                    responses.append(response)
        except Exception as e:
            error_msg = f"处理请求时出错: {str(e)}"
            if not ONLINE_MODE:
                error_msg += " (当前为离线模式)"
            return jsonify({'error': error_msg}), 500
        
        # 找到最终内容
        final_content = ""
        decision_info = []
        
        for resp in responses:
            if resp.get('type') == 'decision':
                decision_info.append(resp.get('content', ''))
            elif resp.get('type') == 'content':
                final_content = resp.get('content', '')
        
        # 构建完整响应
        full_response = ""
        if decision_info:
            full_response += "\n".join(decision_info) + "\n\n"
        full_response += final_content
        
        sessions[session_id]['messages'].append({
            'role': 'assistant',
            'content': full_response,
            'timestamp': datetime.now()
        })
        
        return jsonify({
            'response': full_response,
            'session_id': session_id,
            'decision_info': decision_info
        })
        
    except Exception as e:
        return jsonify({'error': f'处理请求时出错: {str(e)}'}), 500

@app.route('/api/sessions')
def get_sessions():
    """获取所有会话"""
    session_list = []
    for session_id, session_data in sessions.items():
        session_list.append({
            'session_id': session_id,
            'created_at': session_data['created_at'].isoformat(),
            'message_count': len(session_data['messages'])
        })
    return jsonify(session_list)

@app.route('/api/sessions/<session_id>')
def get_session(session_id):
    """获取特定会话的详细信息"""
    if session_id not in sessions:
        return jsonify({'error': '会话不存在'}), 404
    
    session_data = sessions[session_id]
    return jsonify({
        'session_id': session_id,
        'created_at': session_data['created_at'].isoformat(),
        'messages': [
            {
                'role': msg['role'],
                'content': msg['content'],
                'timestamp': msg['timestamp'].isoformat()
            }
            for msg in session_data['messages']
        ]
    })

@app.route('/api/health')
def health_check():
    """健康检查"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'sessions_count': len(sessions)
    })

# 错误处理
@app.errorhandler(404)
def not_found(error):
    return jsonify({'error': '接口不存在'}), 404

@app.errorhandler(500)
def internal_error(error):
    return jsonify({'error': '服务器内部错误'}), 500

if __name__ == '__main__':
    print("🚀 启动智能决策助手API服务...")
    print("📱 访问地址: http://localhost:5000")
    print("🔄 按 Ctrl+C 停止服务")
    print("\n可用接口:")
    print("  GET  /                    - 主页面")
    print("  POST /api/smart_chat     - 智能决策聊天(流式)")
    print("  POST /api/chat           - 简单聊天(非流式)")
    print("  GET  /api/sessions       - 获取会话列表")
    print("  GET  /api/health         - 健康检查")
    
    app.run(debug=True, host='0.0.0.0', port=5000)
