"""
网络连接测试脚本
检查各种可能的连接问题
"""

import requests
import json
import time
import sys

def test_local_server():
    """测试本地服务器"""
    print("🔍 测试本地服务器连接...")
    
    urls = [
        'http://localhost:5000/api/health',
        'http://127.0.0.1:5000/api/health',
        'http://0.0.0.0:5000/api/health'
    ]
    
    for url in urls:
        try:
            print(f"  尝试连接: {url}")
            response = requests.get(url, timeout=5)
            if response.status_code == 200:
                print(f"  ✅ 连接成功: {url}")
                data = response.json()
                print(f"  📊 服务状态: {data}")
                return True
            else:
                print(f"  ❌ HTTP错误: {response.status_code}")
        except requests.exceptions.ConnectionError:
            print(f"  ❌ 连接被拒绝")
        except requests.exceptions.Timeout:
            print(f"  ❌ 连接超时")
        except Exception as e:
            print(f"  ❌ 其他错误: {e}")
    
    return False

def test_chat_api():
    """测试聊天API"""
    print("\n💬 测试聊天API...")
    
    urls = [
        'http://localhost:5000/api/chat',
        'http://127.0.0.1:5000/api/chat'
    ]
    
    test_message = {
        "message": "测试消息",
        "session_id": "test_session"
    }
    
    for url in urls:
        try:
            print(f"  尝试POST: {url}")
            response = requests.post(
                url, 
                json=test_message,
                timeout=10,
                headers={'Content-Type': 'application/json'}
            )
            
            if response.status_code == 200:
                print(f"  ✅ API调用成功")
                data = response.json()
                print(f"  📝 响应长度: {len(data.get('response', ''))}")
                return True
            else:
                print(f"  ❌ HTTP错误: {response.status_code}")
                print(f"  📄 错误内容: {response.text}")
        except requests.exceptions.ConnectionError:
            print(f"  ❌ 连接被拒绝")
        except requests.exceptions.Timeout:
            print(f"  ❌ 连接超时")
        except Exception as e:
            print(f"  ❌ 其他错误: {e}")
    
    return False

def check_dependencies():
    """检查依赖"""
    print("\n📦 检查依赖模块...")
    
    required_modules = [
        'flask',
        'flask_cors',
        'requests'
    ]
    
    missing = []
    for module in required_modules:
        try:
            __import__(module)
            print(f"  ✅ {module}")
        except ImportError:
            print(f"  ❌ {module} - 未安装")
            missing.append(module)
    
    if missing:
        print(f"\n⚠️  缺少依赖: {', '.join(missing)}")
        print("请运行: pip install " + " ".join(missing))
        return False
    
    return True

def check_files():
    """检查必要文件"""
    print("\n📁 检查必要文件...")
    
    import os
    required_files = [
        'smart_chat_api.py',
        'multi_agent_chat.html'
    ]
    
    missing = []
    for file in required_files:
        if os.path.exists(file):
            print(f"  ✅ {file}")
        else:
            print(f"  ❌ {file} - 文件不存在")
            missing.append(file)
    
    if missing:
        print(f"\n⚠️  缺少文件: {', '.join(missing)}")
        return False
    
    return True

def start_server_test():
    """尝试启动服务器进行测试"""
    print("\n🚀 尝试启动测试服务器...")
    
    try:
        import subprocess
        import threading
        import time
        
        # 启动服务器进程
        process = subprocess.Popen(
            [sys.executable, 'smart_chat_api.py'],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        print("  ⏳ 等待服务器启动...")
        time.sleep(3)
        
        # 检查进程是否还在运行
        if process.poll() is None:
            print("  ✅ 服务器进程正在运行")
            
            # 测试连接
            if test_local_server():
                print("  ✅ 服务器响应正常")
                result = True
            else:
                print("  ❌ 服务器无响应")
                result = False
            
            # 终止进程
            process.terminate()
            process.wait()
            return result
        else:
            stdout, stderr = process.communicate()
            print("  ❌ 服务器启动失败")
            print(f"  📄 错误输出: {stderr}")
            return False
            
    except Exception as e:
        print(f"  ❌ 启动测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🔧 智能决策助手 - 网络连接诊断")
    print("=" * 50)
    
    # 检查依赖
    if not check_dependencies():
        return
    
    # 检查文件
    if not check_files():
        return
    
    # 测试现有服务器
    if test_local_server():
        print("\n✅ 发现运行中的服务器")
        if test_chat_api():
            print("\n🎉 所有测试通过！系统运行正常")
        else:
            print("\n⚠️  服务器运行但API有问题")
    else:
        print("\n⚠️  未发现运行中的服务器")
        
        # 尝试启动测试服务器
        if start_server_test():
            print("\n✅ 测试服务器运行正常")
            print("💡 建议：运行 python smart_chat_api.py 启动服务")
        else:
            print("\n❌ 服务器启动失败")
            print("💡 建议：")
            print("   1. 检查端口5000是否被占用")
            print("   2. 检查防火墙设置")
            print("   3. 尝试使用其他端口")
    
    print("\n" + "=" * 50)
    print("🔍 诊断完成")

if __name__ == '__main__':
    main()
