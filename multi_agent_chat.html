<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🤖 多智能体对话系统 - AI助手集合</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            animation: backgroundShift 15s ease-in-out infinite alternate;
        }

        @keyframes backgroundShift {
            0% { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
            50% { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
            100% { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
        }

        .container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(15px);
            border-radius: 25px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
            width: 95%;
            max-width: 1400px;
            height: 90vh;
            display: flex;
            overflow: hidden;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .sidebar {
            width: 350px;
            background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%);
            display: flex;
            flex-direction: column;
            position: relative;
        }

        .sidebar-header {
            padding: 25px 20px;
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            text-align: center;
            position: relative;
        }

        .sidebar-header h2 {
            font-size: 22px;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .sidebar-header p {
            font-size: 14px;
            opacity: 0.9;
        }

        .stats-bar {
            padding: 15px 20px;
            background: rgba(52, 73, 94, 0.8);
            color: white;
            display: flex;
            justify-content: space-between;
            font-size: 12px;
        }

        .stat-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .agent-selector {
            padding: 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .agent-selector label {
            display: block;
            margin-bottom: 10px;
            font-weight: 600;
            color: rgba(255, 255, 255, 0.9);
            font-size: 14px;
        }

        .agent-select {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            font-size: 14px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            transition: all 0.3s ease;
            backdrop-filter: blur(5px);
        }

        .agent-select:focus {
            outline: none;
            border-color: #3498db;
            background: rgba(255, 255, 255, 0.2);
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2);
        }

        .agent-select option {
            background: #2c3e50;
            color: white;
            padding: 10px;
        }

        .agent-info {
            padding: 25px 20px;
            text-align: center;
            color: rgba(255, 255, 255, 0.9);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .agent-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #3498db, #2980b9);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            margin: 0 auto 15px;
            animation: float 3s ease-in-out infinite;
        }

        .agent-info h3 {
            font-size: 18px;
            margin-bottom: 8px;
            color: white;
        }

        .agent-info p {
            font-size: 13px;
            line-height: 1.4;
            opacity: 0.8;
            margin-bottom: 15px;
        }

        .agent-status-info {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            font-size: 12px;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #27ae60;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .chat-area {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: linear-gradient(180deg, #ecf0f1 0%, #bdc3c7 100%);
        }

        .chat-header {
            padding: 25px;
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            text-align: center;
            position: relative;
        }

        .chat-header h2 {
            font-size: 24px;
            margin-bottom: 5px;
        }

        .chat-header p {
            font-size: 14px;
            opacity: 0.9;
        }

        .typing-indicator {
            position: absolute;
            top: 10px;
            right: 20px;
            display: none;
            color: rgba(255, 255, 255, 0.8);
            font-size: 12px;
        }

        .chat-messages {
            flex: 1;
            padding: 25px;
            overflow-y: auto;
            background: linear-gradient(180deg, #f8f9fa 0%, #e9ecef 100%);
            position: relative;
        }

        .message {
            margin: 15px 0;
            padding: 18px 22px;
            border-radius: 20px;
            max-width: 80%;
            word-wrap: break-word;
            position: relative;
            animation: messageSlide 0.3s ease-out;
        }

        @keyframes messageSlide {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .message.user {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            margin-left: auto;
            text-align: right;
            border-bottom-right-radius: 5px;
            box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
        }

        .message.agent {
            background: white;
            color: #2c3e50;
            border: 1px solid #e0e0e0;
            border-bottom-left-radius: 5px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .message-time {
            font-size: 11px;
            opacity: 0.7;
            margin-top: 8px;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .thinking-indicator {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 10px 15px;
            margin: 10px 0;
            border-radius: 4px;
            font-size: 13px;
            color: #1976d2;
            display: flex;
            align-items: center;
            gap: 8px;
            animation: pulse 1.5s ease-in-out infinite;
        }

        .reasoning-log {
            background: #f3e5f5;
            border-left: 4px solid #9c27b0;
            padding: 8px 12px;
            margin: 5px 0;
            border-radius: 4px;
            font-size: 12px;
            color: #7b1fa2;
            display: flex;
            align-items: flex-start;
            gap: 8px;
            opacity: 0;
            animation: fadeInSlide 0.5s ease-out forwards;
        }

        @keyframes fadeInSlide {
            from {
                opacity: 0;
                transform: translateX(-10px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .reasoning-container {
            max-height: 300px;
            overflow-y: auto;
            margin: 10px 0;
            padding: 5px;
            background: #fafafa;
            border-radius: 8px;
            border: 1px solid #e0e0e0;
        }

        .confirmation-container {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            border-radius: 15px;
            padding: 20px;
            margin: 15px 0;
            color: white;
            box-shadow: 0 8px 25px rgba(240, 147, 251, 0.3);
            animation: confirmationSlide 0.5s ease-out;
        }

        @keyframes confirmationSlide {
            from { opacity: 0; transform: translateX(-30px); }
            to { opacity: 1; transform: translateX(0); }
        }

        .confirmation-header {
            display: flex;
            align-items: center;
            font-weight: bold;
            margin-bottom: 15px;
            font-size: 16px;
        }

        .confirmation-header i {
            margin-right: 10px;
            font-size: 18px;
        }

        .confirmation-options {
            display: flex;
            flex-direction: column;
            gap: 10px;
            margin-top: 15px;
        }

        .confirmation-option-button {
            padding: 12px 20px;
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 10px;
            color: white;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: left;
            font-family: inherit;
        }

        .confirmation-option-button:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateX(5px);
        }

        .confirmation-option-button:active {
            transform: translateX(5px) scale(0.98);
        }

        .waiting-message {
            background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
            color: #2d3436;
        }

        .waiting-indicator {
            display: flex;
            align-items: center;
            font-weight: bold;
        }

        .waiting-indicator i {
            margin-right: 10px;
            color: #e17055;
        }

        .reasoning-header {
            font-weight: bold;
            color: #9c27b0;
            margin-bottom: 10px;
            padding: 8px 12px;
            background: #f3e5f5;
            border-radius: 4px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .thinking-indicator .spinner {
            width: 16px;
            height: 16px;
            border: 2px solid #e3f2fd;
            border-top: 2px solid #2196f3;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        .message-content {
            line-height: 1.6;
        }

        .message-content p {
            margin-bottom: 10px;
        }

        .message-content h1, .message-content h2, .message-content h3 {
            margin: 15px 0 10px 0;
            color: #2c3e50;
        }

        .message-content ul, .message-content ol {
            margin: 10px 0;
            padding-left: 20px;
        }

        .message-content blockquote {
            border-left: 4px solid #3498db;
            padding-left: 15px;
            margin: 10px 0;
            font-style: italic;
            color: #7f8c8d;
        }

        .message-content code {
            background: #f8f9fa;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
        }

        .message-content pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
            margin: 10px 0;
        }

        .message-avatar {
            width: 35px;
            height: 35px;
            border-radius: 50%;
            background: linear-gradient(135deg, #3498db, #2980b9);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 14px;
            position: absolute;
            top: -5px;
            left: -45px;
        }

        .message.user .message-avatar {
            right: -45px;
            left: auto;
            background: linear-gradient(135deg, #e74c3c, #c0392b);
        }

        .welcome-message {
            text-align: center;
            padding: 50px 20px;
            color: #7f8c8d;
        }

        .welcome-message h3 {
            margin-bottom: 15px;
            color: #2c3e50;
            font-size: 28px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
        }

        .welcome-message p {
            font-size: 16px;
            line-height: 1.6;
            margin-bottom: 20px;
        }

        .feature-icons {
            display: flex;
            justify-content: center;
            gap: 30px;
            margin-top: 30px;
        }

        .feature-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #3498db, #2980b9);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            animation: float 3s ease-in-out infinite;
            cursor: pointer;
            transition: transform 0.3s ease;
        }

        .feature-icon:hover {
            transform: scale(1.1);
        }

        .feature-icon:nth-child(2) { animation-delay: 0.5s; }
        .feature-icon:nth-child(3) { animation-delay: 1s; }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        .chat-input {
            padding: 25px;
            background: white;
            border-top: 1px solid #e0e0e0;
            display: flex;
            gap: 15px;
            align-items: flex-end;
            position: relative;
        }

        .input-wrapper {
            flex: 1;
            position: relative;
        }

        .chat-input input {
            width: 100%;
            padding: 15px 50px 15px 20px;
            border: 2px solid #e0e0e0;
            border-radius: 25px;
            outline: none;
            font-size: 16px;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }

        .chat-input input:focus {
            border-color: #3498db;
            background: white;
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
        }

        .emoji-btn {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            font-size: 20px;
            cursor: pointer;
            opacity: 0.6;
            transition: opacity 0.3s ease;
        }

        .emoji-btn:hover {
            opacity: 1;
        }

        .chat-input button {
            padding: 15px 25px;
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .chat-input button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
        }

        .chat-input button:disabled {
            background: #bdc3c7;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 20px;
            color: #7f8c8d;
        }

        .loading.show {
            display: block;
        }

        .loading-dots {
            display: inline-block;
        }

        .loading-dots::after {
            content: '';
            animation: dots 1.5s steps(4, end) infinite;
        }

        @keyframes dots {
            0%, 20% { content: ''; }
            40% { content: '.'; }
            60% { content: '..'; }
            80%, 100% { content: '...'; }
        }

        .quick-actions {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
        }

        .quick-action {
            padding: 8px 15px;
            background: rgba(52, 152, 219, 0.1);
            border: 1px solid rgba(52, 152, 219, 0.3);
            border-radius: 20px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            color: #3498db;
        }

        .quick-action:hover {
            background: #3498db;
            color: white;
        }

        @media (max-width: 768px) {
            .container {
                width: 98%;
                height: 95vh;
                flex-direction: column;
            }

            .sidebar {
                width: 100%;
                height: auto;
                min-height: 200px;
            }

            .agent-info {
                padding: 15px;
            }

            .agent-icon {
                width: 50px;
                height: 50px;
                font-size: 20px;
            }

            .feature-icons {
                gap: 20px;
            }

            .feature-icon {
                width: 50px;
                height: 50px;
                font-size: 20px;
            }

            .chat-header h2 {
                font-size: 20px;
            }

            .welcome-message h3 {
                font-size: 24px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="sidebar">
            <div class="sidebar-header">
                <h2><i class="fas fa-robot"></i> 智能体助手</h2>
                <p>选择一个助手开始对话</p>
            </div>
            <div class="agent-info" id="agentInfo">
                <div class="agent-icon" id="agentIcon">
                    <i class="fas fa-brain"></i>
                </div>
                <h3 id="agentName">智能决策助手</h3>
                <p id="agentDescription">自动分析您的需求并选择最合适的专业助手</p>
                <div class="agent-status-info">
                    <span class="status-dot"></span>
                    <span>智能分析中</span>
                </div>
            </div>

            <div class="stats-bar">
                <div class="stat-item">
                    <i class="fas fa-comments"></i>
                    <span id="messageCount">0</span> 条消息
                </div>
                <div class="stat-item">
                    <i class="fas fa-clock"></i>
                    <span id="sessionTime">00:00</span>
                </div>
            </div>
        </div>

        <div class="chat-area">
            <div class="chat-header">
                <h2 id="currentAgent">� 智能决策助手</h2>
                <p id="currentAgentDesc">输入您的需求，我会自动选择最合适的专业助手</p>
                <div class="typing-indicator" id="typingIndicator">
                    <i class="fas fa-circle"></i>
                    <i class="fas fa-circle"></i>
                    <i class="fas fa-circle"></i>
                    智能体正在分析...
                </div>
            </div>

            <div class="chat-messages" id="chatMessages">
                <div class="welcome-message">
                    <h3><i class="fas fa-brain"></i> 智能决策助手</h3>
                    <p>我会自动分析您的需求，并选择最合适的专业助手来帮助您。</p>
                    <p>支持：作文写作、工作报告、公文写作、新媒体文案、小红书内容等</p>
                    <p><strong>直接输入您的需求即可开始！</strong></p>
                    <div class="feature-icons">
                        <div class="feature-icon" title="智能分析">
                            <i class="fas fa-brain"></i>
                        </div>
                        <div class="feature-icon" title="自动选择">
                            <i class="fas fa-magic"></i>
                        </div>
                        <div class="feature-icon" title="专业助手">
                            <i class="fas fa-user-tie"></i>
                        </div>
                    </div>
                </div>
            </div>

            <div class="loading" id="messageLoading">
                <i class="fas fa-brain"></i> 智能体正在思考中<span class="loading-dots"></span>
            </div>

            <div class="chat-input">
                <div class="input-wrapper">
                    <input type="text" id="messageInput" placeholder="请输入您的需求，例如：请帮我写一篇关于春天的作文">
                    <button class="emoji-btn" type="button" title="表情">😊</button>
                </div>
                <button id="sendButton">
                    <i class="fas fa-paper-plane"></i> 发送
                </button>
            </div>
        </div>
    </div>

    <script>
        let messageCount = 0;
        let currentSessionId = 'smart_assistant_' + Date.now(); // 智能助手会话ID

        // 页面加载时初始化
        window.onload = function() {
            setupEventListeners();
            initializeSmartAssistant();
        };

        // 设置事件监听器
        function setupEventListeners() {
            document.getElementById('sendButton').addEventListener('click', sendMessage);
            document.getElementById('messageInput').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    sendMessage();
                }
            });

            // 表情按钮点击事件
            document.querySelector('.emoji-btn').addEventListener('click', function() {
                const input = document.getElementById('messageInput');
                const emojis = ['😊', '👍', '❤️', '😂', '🤔', '👏', '🎉', '💡'];
                const randomEmoji = emojis[Math.floor(Math.random() * emojis.length)];
                input.value += randomEmoji;
                input.focus();
            });
        }

        // 初始化智能助手
        function initializeSmartAssistant() {
            // 添加欢迎消息
            addMessage('您好！我是智能决策助手。请直接告诉我您的需求，我会自动分析并选择最合适的专业助手来帮助您。', 'agent');

            // 聚焦输入框
            document.getElementById('messageInput').focus();

            // 开始计时
            startSessionTimer();
        }

        // 发送消息到智能决策助手
        async function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();

            if (!message) return;

            // 添加用户消息到聊天区域
            addMessage(message, 'user');
            input.value = '';

            // 禁用发送按钮
            document.getElementById('sendButton').disabled = true;
            showTypingIndicator(true);

            // 尝试多种API端点
            const apiEndpoints = [
                '/api/smart_chat',
                '/api/chat',
                'http://localhost:5000/api/smart_chat',
                'http://127.0.0.1:5000/api/smart_chat'
            ];

            let lastError = null;
            let success = false;

            for (const endpoint of apiEndpoints) {
                try {
                    console.log(`尝试连接: ${endpoint}`);

                    // 设置超时
                    const controller = new AbortController();
                    const timeoutId = setTimeout(() => controller.abort(), 10000); // 10秒超时

                    const response = await fetch(endpoint, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            message: message,
                            session_id: currentSessionId
                        }),
                        signal: controller.signal
                    });

                    clearTimeout(timeoutId);

                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }

                    // 检查响应类型
                    const contentType = response.headers.get('content-type');
                    if (contentType && contentType.includes('application/json')) {
                        // JSON响应
                        const data = await response.json();
                        if (data.error) {
                            throw new Error(data.error);
                        }

                        // 处理简单JSON响应
                        handleSimpleResponse(data);
                        success = true;
                        break;
                    } else {
                        // 流式响应
                        await handleStreamResponse(response);
                        success = true;
                        break;
                    }

                } catch (error) {
                    lastError = error;
                    console.warn(`端点 ${endpoint} 失败:`, error.message);

                    if (error.name === 'AbortError') {
                        console.warn('请求超时');
                    }

                    // 如果不是最后一个端点，继续尝试
                    if (endpoint !== apiEndpoints[apiEndpoints.length - 1]) {
                        continue;
                    }
                }
            }

            if (!success) {
                // 所有端点都失败，使用本地模拟
                console.log('所有API端点都失败，使用本地模拟');
                handleLocalResponse(message);
            }

            // 恢复界面状态
            document.getElementById('sendButton').disabled = false;
            showTypingIndicator(false);
            input.focus();
        }

        // 处理流式响应
        async function handleStreamResponse(response) {
            const reader = response.body.getReader();
            const decoder = new TextDecoder();
            let buffer = '';

            while (true) {
                const { done, value } = await reader.read();

                if (done) break;

                buffer += decoder.decode(value, { stream: true });
                const lines = buffer.split('\n');
                buffer = lines.pop(); // 保留不完整的行

                for (const line of lines) {
                    if (line.startsWith('data: ')) {
                        try {
                            const data = JSON.parse(line.slice(6));

                            if (data.type === 'decision') {
                                addDecisionMessage(data.content);
                            } else if (data.type === 'reasoning') {
                                addReasoningStep(data.content);
                            } else if (data.type === 'content') {
                                removeThinkingIndicator();
                                removeReasoningContainer();
                                addMessage(data.content, 'agent');
                            } else if (data.type === 'error') {
                                removeThinkingIndicator();
                                removeReasoningContainer();
                                addMessage('抱歉，处理您的请求时出现了错误：' + data.content, 'agent');
                            } else if (data.type === 'end') {
                                removeThinkingIndicator();
                                removeReasoningContainer();
                                break;
                            }
                        } catch (e) {
                            console.error('解析SSE数据失败:', e);
                        }
                    }
                }
            }
        }

        // 处理简单JSON响应
        function handleSimpleResponse(data) {
            removeThinkingIndicator();
            removeReasoningContainer();

            if (data.decision_info && data.decision_info.length > 0) {
                data.decision_info.forEach(info => {
                    addDecisionMessage(info);
                });
            }

            if (data.response) {
                addMessage(data.response, 'agent');
            } else if (data.content) {
                addMessage(data.content, 'agent');
            } else {
                addMessage('收到响应，但格式不正确', 'agent');
            }
        }

        // 本地模拟响应
        function handleLocalResponse(message) {
            // 简单的本地意图识别
            const agent = analyzeIntentLocal(message);

            addDecisionMessage(`🤖 意图分析：${agent.reason}`);
            addDecisionMessage(`📋 选择智能体：${agent.name}`);
            addDecisionMessage(`🚀 开始处理您的请求...`);

            setTimeout(() => {
                removeThinkingIndicator();
                removeReasoningContainer();

                const response = generateLocalResponse(message, agent);
                addMessage(response, 'agent');
            }, 1500);
        }

        // 本地意图分析
        function analyzeIntentLocal(input) {
            const rules = [
                {keywords: ["作文", "写作", "文章"], name: "作文助手", reason: "检测到写作相关关键词"},
                {keywords: ["日报", "工作"], name: "日报助手", reason: "检测到日报相关关键词"},
                {keywords: ["周报"], name: "周报助手", reason: "检测到周报相关关键词"},
                {keywords: ["月报"], name: "月报助手", reason: "检测到月报相关关键词"},
                {keywords: ["公文", "通知"], name: "公文助手", reason: "检测到公文相关关键词"},
                {keywords: ["文案", "推广"], name: "文案助手", reason: "检测到文案相关关键词"},
                {keywords: ["小红书"], name: "小红书助手", reason: "检测到小红书相关关键词"}
            ];

            for (const rule of rules) {
                if (rule.keywords.some(keyword => input.includes(keyword))) {
                    return rule;
                }
            }

            return {name: "通用助手", reason: "未匹配到特定类型，使用通用助手"};
        }

        // 生成本地响应
        function generateLocalResponse(input, agent) {
            // 根据不同类型生成专业回复
            if (agent.name.includes("作文")) {
                return generateWritingResponse(input);
            } else if (agent.name.includes("日报")) {
                return generateDailyReportResponse(input);
            } else if (agent.name.includes("周报")) {
                return generateWeeklyReportResponse(input);
            } else if (agent.name.includes("月报")) {
                return generateMonthlyReportResponse(input);
            } else if (agent.name.includes("公文")) {
                return generateOfficialDocResponse(input);
            } else if (agent.name.includes("文案")) {
                return generateCopywritingResponse(input);
            } else if (agent.name.includes("小红书")) {
                return generateXiaohongshuResponse(input);
            } else {
                return generateGeneralResponse(input, agent);
            }
        }

        // 写作助手响应
        function generateWritingResponse(input) {
            const topic = extractTopic(input);
            return `# 📝 作文创作指导

## 主题分析
根据您的需求"${input}"，我为您提供以下写作指导：

## 🎯 写作思路
1. **确定中心思想** - ${topic}的核心要表达什么
2. **构建文章结构** - 开头、正文、结尾的安排
3. **选择写作手法** - 记叙、描写、议论、抒情的运用
4. **丰富内容细节** - 具体事例和生动描述

## ✍️ 写作建议
- **开头**：可以用设问、引用、场景描写等方式吸引读者
- **正文**：围绕中心思想，层次分明地展开论述
- **结尾**：呼应开头，升华主题，给读者留下深刻印象

## 📚 参考结构
1. 引入话题（100-150字）
2. 主体内容（300-400字）
3. 总结升华（50-100字）

## 💡 写作技巧
- 使用生动的比喻和拟人手法
- 适当运用排比、对偶等修辞
- 注意段落之间的过渡和衔接
- 语言要准确、简洁、有感染力

祝您写作顺利！如需更具体的指导，请告诉我更多细节。`;
        }

        // 日报助手响应
        function generateDailyReportResponse(input) {
            const today = new Date().toLocaleDateString('zh-CN');
            return `# 📊 工作日报模板

## 📅 ${today} 工作日报

### ✅ 今日完成工作
1. **主要任务一**
   - 具体完成内容
   - 达成效果/结果

2. **主要任务二**
   - 具体完成内容
   - 达成效果/结果

3. **其他工作**
   - 日常事务处理
   - 会议参与情况

### ⚠️ 遇到的问题
- **问题描述**：具体问题说明
- **影响程度**：对工作进度的影响
- **解决方案**：已采取或计划采取的措施

### 📋 明日工作计划
1. **优先级任务**
   - 任务名称
   - 预期完成时间
   - 所需资源

2. **常规任务**
   - 日常工作安排
   - 会议安排

### 📈 工作数据
- 完成任务数：X项
- 工作时长：X小时
- 效率评估：良好/一般/需改进

### 💭 其他说明
- 需要协调的事项
- 需要支持的资源
- 其他备注信息

---
*报告人：[姓名] | 部门：[部门名称] | 日期：${today}*`;
        }

        // 周报助手响应
        function generateWeeklyReportResponse(input) {
            const weekStart = getWeekStart();
            const weekEnd = getWeekEnd();
            return `# 📈 工作周报

## 📅 ${weekStart} - ${weekEnd} 周工作总结

### 🎯 本周工作概述
本周主要围绕以下几个方面开展工作：

### ✅ 主要完成工作
1. **重点项目进展**
   - 项目名称：[具体项目]
   - 完成情况：[完成度百分比]
   - 关键成果：[具体成果描述]

2. **日常工作完成情况**
   - 任务类型：[具体任务]
   - 完成质量：[质量评估]
   - 时间效率：[效率分析]

### 📊 工作数据统计
- 完成任务总数：X项
- 按时完成率：X%
- 工作总时长：X小时
- 会议参与：X次

### ⚠️ 问题与挑战
1. **遇到的主要问题**
   - 问题描述
   - 影响分析
   - 解决进展

2. **需要协调的事项**
   - 跨部门协作需求
   - 资源支持需求

### 📋 下周工作计划
1. **重点工作安排**
   - 优先级任务
   - 预期目标
   - 时间安排

2. **常规工作计划**
   - 日常任务安排
   - 会议计划

### 💡 改进建议
- 工作方法优化
- 效率提升措施
- 技能提升计划

---
*周报期间：${weekStart} - ${weekEnd} | 汇报人：[姓名]*`;
        }

        // 获取本周开始日期
        function getWeekStart() {
            const now = new Date();
            const day = now.getDay();
            const diff = now.getDate() - day + (day === 0 ? -6 : 1);
            return new Date(now.setDate(diff)).toLocaleDateString('zh-CN');
        }

        // 获取本周结束日期
        function getWeekEnd() {
            const now = new Date();
            const day = now.getDay();
            const diff = now.getDate() - day + (day === 0 ? 0 : 7);
            return new Date(now.setDate(diff)).toLocaleDateString('zh-CN');
        }

        // 月报助手响应
        function generateMonthlyReportResponse(input) {
            const currentMonth = new Date().toLocaleDateString('zh-CN', {year: 'numeric', month: 'long'});
            return `# 📋 月度工作报告

## 📅 ${currentMonth}工作总结

### 🎯 月度工作概述
本月工作围绕年度目标，重点完成以下几个方面：

### ✅ 主要工作成果
1. **重点项目完成情况**
   - 项目A：完成度90%，超出预期
   - 项目B：完成度85%，按计划推进
   - 项目C：完成度75%，需要加强

2. **业务指标达成情况**
   - 关键指标1：达成率110%
   - 关键指标2：达成率95%
   - 关键指标3：达成率88%

### 📊 数据统计分析
- 月度任务完成率：X%
- 工作效率提升：X%
- 客户满意度：X分
- 团队协作评分：X分

### 💡 工作亮点
1. **创新举措**
   - 具体创新内容
   - 带来的效果

2. **优秀表现**
   - 突出成绩
   - 获得认可

### ⚠️ 存在问题及改进
1. **主要问题**
   - 问题描述
   - 原因分析
   - 改进措施

2. **待优化事项**
   - 流程优化需求
   - 技能提升计划

### 📈 下月工作规划
1. **重点工作目标**
   - 核心任务
   - 预期成果
   - 关键节点

2. **能力提升计划**
   - 学习目标
   - 培训安排

### 🎖️ 总结与展望
本月工作总体达到预期目标，在XXX方面取得突破，为下月工作奠定了良好基础。

---
*报告期间：${currentMonth} | 汇报人：[姓名] | 部门：[部门名称]*`;
        }

        // 公文写作助手响应
        function generateOfficialDocResponse(input) {
            const docType = extractDocType(input);
            return `# 📄 ${docType}

## 标准格式模板

### 📋 文件头部
**[单位名称]**
**${docType}**

文件编号：[单位简称]〔2024〕XX号

---

### 📝 正文内容

**关于XXX的${docType}**

各部门、各单位：

根据XXX要求，结合我单位实际情况，现就XXX事项${docType}如下：

**一、基本情况**
（简要说明背景和现状）

**二、主要内容**
1. 具体事项一
2. 具体事项二
3. 具体事项三

**三、工作要求**
1. 高度重视，认真落实
2. 明确责任，确保效果
3. 及时反馈，持续改进

**四、其他事项**
（补充说明和注意事项）

特此${docType}。

---

### 📅 文件尾部
**[单位名称]**
**2024年X月X日**

### 📋 抄送信息
抄送：相关部门
发文机关：[单位名称]办公室

### 💡 写作要点
- 语言要规范、准确、简洁
- 结构要清晰、逻辑性强
- 格式要符合公文标准
- 内容要具体、可操作

### 📚 常用公文类型
- 通知：传达事项、布置工作
- 通报：表彰先进、批评错误
- 函：商洽工作、询问问题
- 请示：请求指示、批准
- 报告：汇报工作、反映情况`;
        }

        // 文案助手响应
        function generateCopywritingResponse(input) {
            const product = extractProduct(input);
            return `# 📱 ${product}推广文案

## 🎯 文案策略

### 💡 核心卖点
1. **独特价值** - 与众不同的优势
2. **用户痛点** - 解决的核心问题
3. **情感共鸣** - 触动用户内心

### ✍️ 文案模板

#### 🔥 标题文案
- "XXX，让生活更美好"
- "不只是XXX，更是XXX"
- "XXX新体验，XXX新选择"

#### 📝 正文文案
**开头吸引**
你是否还在为XXX而烦恼？
现在，一切都将改变！

**产品介绍**
[产品名称]，专为XXX人群打造
- 特色功能1：具体描述
- 特色功能2：具体描述
- 特色功能3：具体描述

**用户证言**
"使用后，我的XXX得到了显著改善" - 用户A
"真的很好用，推荐给大家" - 用户B

**行动召唤**
立即体验，享受XXX优惠！
限时特价，仅需XXX元！

### 📊 不同平台适配

#### 微信朋友圈
简洁有力，配图精美
文字控制在100字以内

#### 微博推广
话题标签，互动性强
#XXX# 让生活更精彩

#### 抖音短视频
节奏感强，视觉冲击
15秒内抓住注意力

### 🎨 创意方向
1. **场景化** - 展示使用场景
2. **故事化** - 讲述品牌故事
3. **互动化** - 鼓励用户参与
4. **个性化** - 突出独特性

### 📈 效果优化
- A/B测试不同版本
- 监控转化数据
- 持续优化调整
- 收集用户反馈`;
        }

        // 小红书助手响应
        function generateXiaohongshuResponse(input) {
            const topic = extractTopic(input);
            return `# 🌸 小红书种草文案

## 📱 ${topic}种草攻略

### ✨ 标题党必备
- 🔥"这个XXX绝了！用完直接路转粉"
- 💕"姐妹们！这个XXX真的yyds"
- 🎉"终于找到了！XXX界的天花板"
- 😍"不买后悔系列！XXX使用感受"

### 📝 正文模板

**开头抓眼球**
姐妹们！今天必须来分享这个宝藏XXX！
真的是用了就回不去的那种～

**产品详细介绍**
🌟 产品名称：[具体名称]
💰 价格：XX元（性价比超高！）
🛒 购买渠道：[具体渠道]

**使用体验分享**
📍 使用感受：
- 质感：超级好！
- 效果：立竿见影
- 持久度：一整天都OK

📍 优点总结：
✅ 优点1：具体描述
✅ 优点2：具体描述
✅ 优点3：具体描述

📍 小缺点：
❌ 诚实分享一些小不足

**种草理由**
真的强烈推荐给大家！
特别适合XXX的姐妹
性价比真的很高，值得入手！

### 🏷️ 热门标签
#种草 #好物推荐 #${topic} #必买清单
#性价比 #学生党 #上班族 #生活好物

### 📸 配图建议
1. **产品实拍** - 多角度展示
2. **使用对比** - 前后效果
3. **场景搭配** - 生活化场景
4. **细节特写** - 突出卖点

### 💡 小红书爆款技巧
- 真实分享，避免过度营销
- 多用emoji，增加亲和力
- 互动回复，提高活跃度
- 定期更新，保持曝光

### 🎯 目标用户
- 年龄：18-35岁
- 性别：主要女性用户
- 兴趣：美妆、时尚、生活
- 消费：追求品质和性价比`;
        }

        // 通用助手响应
        function generateGeneralResponse(input, agent) {
            return `# 🤖 ${agent.name}为您服务

## 📋 您的请求
"${input}"

## 💡 智能分析
我已经分析了您的需求，这是一个关于**${extractKeywords(input).join('、')}**的请求。

## 🎯 专业建议
基于您的需求，我建议从以下几个方面来处理：

### 1. 需求分析
- 明确具体目标
- 确定关键要求
- 评估可行性

### 2. 解决方案
- 方案A：[具体方案]
- 方案B：[备选方案]
- 方案C：[创新方案]

### 3. 实施步骤
1. 准备阶段：收集资料，制定计划
2. 执行阶段：按步骤实施
3. 检查阶段：评估效果，调整优化
4. 总结阶段：经验总结，持续改进

## 📚 相关资源
- 参考资料：[相关链接或文档]
- 工具推荐：[实用工具]
- 学习建议：[技能提升]

## 🔄 后续支持
如果您需要更详细的帮助，请：
1. 提供更多具体信息
2. 明确您的具体需求
3. 告诉我您希望的输出格式

我会根据您的反馈提供更精准的帮助！`;
        }

        // 辅助函数
        function extractTopic(input) {
            if (input.includes('春天')) return '春天';
            if (input.includes('夏天')) return '夏天';
            if (input.includes('秋天')) return '秋天';
            if (input.includes('冬天')) return '冬天';
            if (input.includes('学校')) return '学校生活';
            if (input.includes('友谊')) return '友谊';
            if (input.includes('梦想')) return '梦想';
            return '生活感悟';
        }

        function extractDocType(input) {
            if (input.includes('通知')) return '通知';
            if (input.includes('通报')) return '通报';
            if (input.includes('函')) return '函';
            if (input.includes('请示')) return '请示';
            if (input.includes('报告')) return '报告';
            if (input.includes('会议')) return '会议通知';
            return '通知';
        }

        function extractProduct(input) {
            if (input.includes('产品')) return '产品';
            if (input.includes('服务')) return '服务';
            if (input.includes('品牌')) return '品牌';
            if (input.includes('活动')) return '活动';
            return '产品';
        }

        function extractKeywords(input) {
            const keywords = [];
            const commonWords = ['写作', '报告', '文案', '推广', '创作', '内容', '策划', '设计'];
            commonWords.forEach(word => {
                if (input.includes(word)) keywords.push(word);
            });
            return keywords.length > 0 ? keywords : ['内容创作'];
        }

        // 添加决策信息消息
        function addDecisionMessage(content) {
            const chatMessages = document.getElementById('chatMessages');
            const decisionElement = document.createElement('div');
            decisionElement.className = 'thinking-indicator';
            decisionElement.innerHTML = `
                <i class="fas fa-brain"></i>
                <span>${content}</span>
            `;
            chatMessages.appendChild(decisionElement);
            chatMessages.scrollTop = chatMessages.scrollHeight;

            // 3秒后自动移除决策信息
            setTimeout(() => {
                if (decisionElement.parentNode) {
                    decisionElement.remove();
                }
            }, 3000);
        }



        // 添加消息到聊天区域
        function addMessage(content, type) {
            const chatMessages = document.getElementById('chatMessages');
            const messageElement = document.createElement('div');
            messageElement.className = `message ${type}`;

            const avatar = document.createElement('div');
            avatar.className = 'message-avatar';
            avatar.innerHTML = type === 'user' ? '<i class="fas fa-user"></i>' : '<i class="fas fa-robot"></i>';

            const messageContent = document.createElement('div');

            // 处理内容格式
            let processedContent = content;
            if (type === 'agent') {
                // 对于智能体消息，使用Markdown渲染
                try {
                    processedContent = marked.parse(content);
                } catch (error) {
                    console.warn('Markdown parsing failed:', error);
                    // 如果Markdown解析失败，至少处理换行符
                    processedContent = content.replace(/\n/g, '<br>');
                }
            } else {
                // 对于用户消息，只处理换行符
                processedContent = content.replace(/\n/g, '<br>');
            }

            const messageHTML = `
                <div class="message-content">${processedContent}</div>
                <div class="message-time">
                    <i class="fas fa-clock"></i>
                    ${new Date().toLocaleTimeString()}
                </div>
            `;

            messageContent.innerHTML = messageHTML;
            messageElement.appendChild(avatar);
            messageElement.appendChild(messageContent);
            chatMessages.appendChild(messageElement);

            // 滚动到底部
            chatMessages.scrollTop = chatMessages.scrollHeight;

            // 更新消息计数
            messageCount++;
            updateMessageCount(messageCount);

            return messageElement;
        }

        // 添加推理容器
        function addReasoningContainer() {
            const chatMessages = document.getElementById('chatMessages');
            const reasoningContainer = document.createElement('div');
            reasoningContainer.id = 'reasoning-container';
            reasoningContainer.innerHTML = `
                <div class="reasoning-header">
                    <i class="fas fa-brain"></i>
                    <span>AI推理过程</span>
                </div>
                <div class="reasoning-container" id="reasoning-content">
                </div>
            `;
            chatMessages.appendChild(reasoningContainer);

            // 滚动到底部
            chatMessages.scrollTop = chatMessages.scrollHeight;

            return reasoningContainer;
        }

        // 添加推理步骤
        function addReasoningStep(content) {
            let reasoningContent = document.getElementById('reasoning-content');

            // 如果推理容器不存在，创建它
            if (!reasoningContent) {
                addReasoningContainer();
                reasoningContent = document.getElementById('reasoning-content');
            }

            const reasoningStep = document.createElement('div');
            reasoningStep.className = 'reasoning-log';
            reasoningStep.innerHTML = `
                <span>${content}</span>
            `;

            reasoningContent.appendChild(reasoningStep);

            // 滚动到底部
            const chatMessages = document.getElementById('chatMessages');
            chatMessages.scrollTop = chatMessages.scrollHeight;

            // 如果推理步骤太多，移除最早的
            const steps = reasoningContent.querySelectorAll('.reasoning-log');
            if (steps.length > 15) {
                steps[0].remove();
            }
        }

        // 移除推理容器
        function removeReasoningContainer() {
            const container = document.getElementById('reasoning-container');
            if (container) {
                // 添加淡出效果
                container.style.opacity = '0.5';
                setTimeout(() => {
                    container.remove();
                }, 500);
            }
        }

        // 添加确认问题
        function addConfirmationQuestion(question) {
            const chatMessages = document.getElementById('chatMessages');
            const confirmationContainer = document.createElement('div');
            confirmationContainer.id = 'confirmation-container';
            confirmationContainer.className = 'confirmation-container';
            confirmationContainer.innerHTML = `
                <div class="confirmation-header">
                    <i class="fas fa-question-circle"></i>
                    <span>${question}</span>
                </div>
                <div class="confirmation-options" id="confirmation-options"></div>
            `;
            chatMessages.appendChild(confirmationContainer);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        // 添加可点击的确认选项
        function addConfirmationOption(optionData) {
            const confirmationOptions = document.getElementById('confirmation-options');
            if (!confirmationOptions) return;

            const optionElement = document.createElement('button');
            optionElement.className = 'confirmation-option-button';
            optionElement.textContent = optionData.text;
            optionElement.onclick = () => selectConfirmationOption(optionData.id, optionData.text);

            confirmationOptions.appendChild(optionElement);

            // 滚动到底部
            const chatMessages = document.getElementById('chatMessages');
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        // 处理确认选项选择
        async function selectConfirmationOption(optionId, optionText) {
            // 显示用户的选择
            addMessage(`我选择：${optionText}`, 'user');

            // 移除确认容器
            removeConfirmationContainer();

            // 发送选择结果到后端
            const confirmMessage = `CONFIRM_CHOICE:${optionId}|${optionText}`;
            await sendConfirmationChoice(confirmMessage);
        }

        // 发送确认选择到后端
        async function sendConfirmationChoice(confirmMessage) {
            if (!currentAgentId) return;

            try {
                showTypingIndicator(true);

                const response = await fetch('/api/chat/stream', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        agent_id: currentAgentId,
                        message: confirmMessage,
                        session_id: currentSessionId
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const reader = response.body.getReader();
                const decoder = new TextDecoder();

                while (true) {
                    const { done, value } = await reader.read();
                    if (done) break;

                    const chunk = decoder.decode(value);
                    const lines = chunk.split('\n');

                    for (const line of lines) {
                        if (line.startsWith('data: ')) {
                            try {
                                const data = JSON.parse(line.slice(6));

                                if (data.type === 'reasoning') {
                                    addReasoningStep(data.content);
                                } else if (data.type === 'confirmation_question') {
                                    addConfirmationQuestion(data.content);
                                } else if (data.type === 'confirmation_option') {
                                    addConfirmationOption(data.content);
                                } else if (data.type === 'waiting') {
                                    addWaitingMessage(data.content);
                                    return; // 等待下一次选择
                                } else if (data.type === 'content') {
                                    removeThinkingIndicator();
                                    removeReasoningContainer();
                                    addMessage(data.content, 'agent');
                                    return; // 完成
                                } else if (data.type === 'error') {
                                    addMessage('抱歉，处理您的选择时出现了错误：' + data.content, 'agent');
                                    return;
                                }
                            } catch (e) {
                                console.error('解析确认响应失败:', e);
                            }
                        }
                    }
                }

            } catch (error) {
                console.error('发送确认选择失败:', error);
                addMessage('网络连接错误，请稍后重试。', 'agent');
            } finally {
                showTypingIndicator(false);
            }
        }

        // 移除确认容器
        function removeConfirmationContainer() {
            const confirmationContainer = document.getElementById('confirmation-container');
            if (confirmationContainer) {
                confirmationContainer.remove();
            }
        }

        // 添加等待消息
        function addWaitingMessage(content) {
            const chatMessages = document.getElementById('chatMessages');
            const waitingElement = document.createElement('div');
            waitingElement.className = 'message agent waiting-message';
            waitingElement.innerHTML = `
                <div class="message-avatar">
                    <i class="fas fa-robot"></i>
                </div>
                <div class="message-content">
                    <div class="waiting-indicator">
                        <i class="fas fa-hourglass-half fa-spin"></i>
                        ${content}
                    </div>
                    <div class="message-time">
                        <i class="fas fa-clock"></i>
                        ${new Date().toLocaleTimeString()}
                    </div>
                </div>
            `;
            chatMessages.appendChild(waitingElement);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        // 添加思考指示器（简单状态）
        function addThinkingIndicator(content) {
            const chatMessages = document.getElementById('chatMessages');
            const thinkingElement = document.createElement('div');
            thinkingElement.className = 'thinking-indicator';
            thinkingElement.innerHTML = `
                <div class="spinner"></div>
                <span>${content}</span>
            `;
            thinkingElement.id = 'thinking-indicator';
            chatMessages.appendChild(thinkingElement);

            // 滚动到底部
            chatMessages.scrollTop = chatMessages.scrollHeight;

            return thinkingElement;
        }

        // 移除思考指示器
        function removeThinkingIndicator() {
            const indicator = document.getElementById('thinking-indicator');
            if (indicator) {
                indicator.remove();
            }
        }

        // 显示/隐藏加载状态
        function showLoading(show) {
            const loading = document.getElementById('messageLoading');
            if (show) {
                loading.classList.add('show');
            } else {
                loading.classList.remove('show');
            }
        }

        // 显示/隐藏打字指示器
        function showTypingIndicator(show) {
            const indicator = document.getElementById('typingIndicator');
            indicator.style.display = show ? 'block' : 'none';
        }

        // 显示错误消息
        function showError(message) {
            addMessage('系统错误：' + message, 'agent');
        }

        // 会话计时器
        let sessionTimer = null;
        let sessionStartTime = null;

        function startSessionTimer() {
            if (sessionTimer) return; // 已经在计时

            sessionStartTime = Date.now();
            sessionTimer = setInterval(() => {
                const elapsed = Math.floor((Date.now() - sessionStartTime) / 1000);
                const minutes = Math.floor(elapsed / 60);
                const seconds = elapsed % 60;
                document.getElementById('sessionTime').textContent =
                    `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            }, 1000);
        }

        function stopSessionTimer() {
            if (sessionTimer) {
                clearInterval(sessionTimer);
                sessionTimer = null;
                sessionStartTime = null;
                document.getElementById('sessionTime').textContent = '00:00';
            }
        }

        // 更新消息计数
        function updateMessageCount(count) {
            document.getElementById('messageCount').textContent = count;
        }

        // 添加一些交互效果
        document.addEventListener('DOMContentLoaded', function() {
            // 为特性图标添加点击效果
            document.querySelectorAll('.feature-icon').forEach(icon => {
                icon.addEventListener('click', function() {
                    this.style.transform = 'scale(0.9)';
                    setTimeout(() => {
                        this.style.transform = '';
                    }, 150);
                });
            });

            // 添加键盘快捷键
            document.addEventListener('keydown', function(e) {
                // Ctrl + Enter 发送消息
                if (e.ctrlKey && e.key === 'Enter') {
                    sendMessage();
                }

                // ESC 清空输入框
                if (e.key === 'Escape') {
                    document.getElementById('messageInput').value = '';
                }
            });
        });

        // 添加一些实用功能
        function clearChat() {
            document.getElementById('chatMessages').innerHTML = '';
            messageCount = 0;
            updateMessageCount(0);
        }

        function exportChat() {
            const messages = document.querySelectorAll('.message');
            let chatText = `多智能体对话记录 - ${new Date().toLocaleString()}\n\n`;

            messages.forEach(msg => {
                const type = msg.classList.contains('user') ? '用户' : '智能体';
                const content = msg.querySelector('div').textContent;
                const time = msg.querySelector('.message-time').textContent.replace('🕐', '').trim();
                chatText += `[${time}] ${type}: ${content}\n`;
            });

            const blob = new Blob([chatText], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `chat-${new Date().getTime()}.txt`;
            a.click();
            URL.revokeObjectURL(url);
        }
    </script>
</body>
</html>