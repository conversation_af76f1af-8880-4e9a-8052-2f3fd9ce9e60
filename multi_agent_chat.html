<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🤖 多智能体对话系统 - AI助手集合</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            animation: backgroundShift 15s ease-in-out infinite alternate;
        }

        @keyframes backgroundShift {
            0% { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
            50% { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
            100% { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
        }

        .container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(15px);
            border-radius: 25px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
            width: 95%;
            max-width: 1400px;
            height: 90vh;
            display: flex;
            overflow: hidden;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .sidebar {
            width: 350px;
            background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%);
            display: flex;
            flex-direction: column;
            position: relative;
        }

        .sidebar-header {
            padding: 25px 20px;
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            text-align: center;
            position: relative;
        }

        .sidebar-header h2 {
            font-size: 22px;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .sidebar-header p {
            font-size: 14px;
            opacity: 0.9;
        }

        .stats-bar {
            padding: 15px 20px;
            background: rgba(52, 73, 94, 0.8);
            color: white;
            display: flex;
            justify-content: space-between;
            font-size: 12px;
        }

        .stat-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .agent-selector {
            padding: 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .agent-selector label {
            display: block;
            margin-bottom: 10px;
            font-weight: 600;
            color: rgba(255, 255, 255, 0.9);
            font-size: 14px;
        }

        .agent-select {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            font-size: 14px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            transition: all 0.3s ease;
            backdrop-filter: blur(5px);
        }

        .agent-select:focus {
            outline: none;
            border-color: #3498db;
            background: rgba(255, 255, 255, 0.2);
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2);
        }

        .agent-select option {
            background: #2c3e50;
            color: white;
            padding: 10px;
        }

        .agent-info {
            padding: 25px 20px;
            text-align: center;
            color: rgba(255, 255, 255, 0.9);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .agent-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #3498db, #2980b9);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            margin: 0 auto 15px;
            animation: float 3s ease-in-out infinite;
        }

        .agent-info h3 {
            font-size: 18px;
            margin-bottom: 8px;
            color: white;
        }

        .agent-info p {
            font-size: 13px;
            line-height: 1.4;
            opacity: 0.8;
            margin-bottom: 15px;
        }

        .agent-status-info {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            font-size: 12px;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #27ae60;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .chat-area {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: linear-gradient(180deg, #ecf0f1 0%, #bdc3c7 100%);
        }

        .chat-header {
            padding: 25px;
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            text-align: center;
            position: relative;
        }

        .chat-header h2 {
            font-size: 24px;
            margin-bottom: 5px;
        }

        .chat-header p {
            font-size: 14px;
            opacity: 0.9;
        }

        .typing-indicator {
            position: absolute;
            top: 10px;
            right: 20px;
            display: none;
            color: rgba(255, 255, 255, 0.8);
            font-size: 12px;
        }

        .chat-messages {
            flex: 1;
            padding: 25px;
            overflow-y: auto;
            background: linear-gradient(180deg, #f8f9fa 0%, #e9ecef 100%);
            position: relative;
        }

        .message {
            margin: 15px 0;
            padding: 18px 22px;
            border-radius: 20px;
            max-width: 80%;
            word-wrap: break-word;
            position: relative;
            animation: messageSlide 0.3s ease-out;
        }

        @keyframes messageSlide {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .message.user {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            margin-left: auto;
            text-align: right;
            border-bottom-right-radius: 5px;
            box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
        }

        .message.agent {
            background: white;
            color: #2c3e50;
            border: 1px solid #e0e0e0;
            border-bottom-left-radius: 5px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .message-time {
            font-size: 11px;
            opacity: 0.7;
            margin-top: 8px;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .thinking-indicator {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 10px 15px;
            margin: 10px 0;
            border-radius: 4px;
            font-size: 13px;
            color: #1976d2;
            display: flex;
            align-items: center;
            gap: 8px;
            animation: pulse 1.5s ease-in-out infinite;
        }

        .reasoning-log {
            background: #f3e5f5;
            border-left: 4px solid #9c27b0;
            padding: 8px 12px;
            margin: 5px 0;
            border-radius: 4px;
            font-size: 12px;
            color: #7b1fa2;
            display: flex;
            align-items: flex-start;
            gap: 8px;
            opacity: 0;
            animation: fadeInSlide 0.5s ease-out forwards;
        }

        @keyframes fadeInSlide {
            from {
                opacity: 0;
                transform: translateX(-10px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .reasoning-container {
            max-height: 300px;
            overflow-y: auto;
            margin: 10px 0;
            padding: 5px;
            background: #fafafa;
            border-radius: 8px;
            border: 1px solid #e0e0e0;
        }

        .confirmation-container {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            border-radius: 15px;
            padding: 20px;
            margin: 15px 0;
            color: white;
            box-shadow: 0 8px 25px rgba(240, 147, 251, 0.3);
            animation: confirmationSlide 0.5s ease-out;
        }

        @keyframes confirmationSlide {
            from { opacity: 0; transform: translateX(-30px); }
            to { opacity: 1; transform: translateX(0); }
        }

        .confirmation-header {
            display: flex;
            align-items: center;
            font-weight: bold;
            margin-bottom: 15px;
            font-size: 16px;
        }

        .confirmation-header i {
            margin-right: 10px;
            font-size: 18px;
        }

        .confirmation-options {
            display: flex;
            flex-direction: column;
            gap: 10px;
            margin-top: 15px;
        }

        .confirmation-option-button {
            padding: 12px 20px;
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 10px;
            color: white;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: left;
            font-family: inherit;
        }

        .confirmation-option-button:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateX(5px);
        }

        .confirmation-option-button:active {
            transform: translateX(5px) scale(0.98);
        }

        .waiting-message {
            background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
            color: #2d3436;
        }

        .waiting-indicator {
            display: flex;
            align-items: center;
            font-weight: bold;
        }

        .waiting-indicator i {
            margin-right: 10px;
            color: #e17055;
        }

        .reasoning-header {
            font-weight: bold;
            color: #9c27b0;
            margin-bottom: 10px;
            padding: 8px 12px;
            background: #f3e5f5;
            border-radius: 4px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .thinking-indicator .spinner {
            width: 16px;
            height: 16px;
            border: 2px solid #e3f2fd;
            border-top: 2px solid #2196f3;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        .message-content {
            line-height: 1.6;
        }

        .message-content p {
            margin-bottom: 10px;
        }

        .message-content h1, .message-content h2, .message-content h3 {
            margin: 15px 0 10px 0;
            color: #2c3e50;
        }

        .message-content ul, .message-content ol {
            margin: 10px 0;
            padding-left: 20px;
        }

        .message-content blockquote {
            border-left: 4px solid #3498db;
            padding-left: 15px;
            margin: 10px 0;
            font-style: italic;
            color: #7f8c8d;
        }

        .message-content code {
            background: #f8f9fa;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
        }

        .message-content pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
            margin: 10px 0;
        }

        .message-avatar {
            width: 35px;
            height: 35px;
            border-radius: 50%;
            background: linear-gradient(135deg, #3498db, #2980b9);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 14px;
            position: absolute;
            top: -5px;
            left: -45px;
        }

        .message.user .message-avatar {
            right: -45px;
            left: auto;
            background: linear-gradient(135deg, #e74c3c, #c0392b);
        }

        .welcome-message {
            text-align: center;
            padding: 50px 20px;
            color: #7f8c8d;
        }

        .welcome-message h3 {
            margin-bottom: 15px;
            color: #2c3e50;
            font-size: 28px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
        }

        .welcome-message p {
            font-size: 16px;
            line-height: 1.6;
            margin-bottom: 20px;
        }

        .feature-icons {
            display: flex;
            justify-content: center;
            gap: 30px;
            margin-top: 30px;
        }

        .feature-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #3498db, #2980b9);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            animation: float 3s ease-in-out infinite;
            cursor: pointer;
            transition: transform 0.3s ease;
        }

        .feature-icon:hover {
            transform: scale(1.1);
        }

        .feature-icon:nth-child(2) { animation-delay: 0.5s; }
        .feature-icon:nth-child(3) { animation-delay: 1s; }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        .chat-input {
            padding: 25px;
            background: white;
            border-top: 1px solid #e0e0e0;
            display: flex;
            gap: 15px;
            align-items: flex-end;
            position: relative;
        }

        .input-wrapper {
            flex: 1;
            position: relative;
        }

        .chat-input input {
            width: 100%;
            padding: 15px 50px 15px 20px;
            border: 2px solid #e0e0e0;
            border-radius: 25px;
            outline: none;
            font-size: 16px;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }

        .chat-input input:focus {
            border-color: #3498db;
            background: white;
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
        }

        .emoji-btn {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            font-size: 20px;
            cursor: pointer;
            opacity: 0.6;
            transition: opacity 0.3s ease;
        }

        .emoji-btn:hover {
            opacity: 1;
        }

        .chat-input button {
            padding: 15px 25px;
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .chat-input button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
        }

        .chat-input button:disabled {
            background: #bdc3c7;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 20px;
            color: #7f8c8d;
        }

        .loading.show {
            display: block;
        }

        .loading-dots {
            display: inline-block;
        }

        .loading-dots::after {
            content: '';
            animation: dots 1.5s steps(4, end) infinite;
        }

        @keyframes dots {
            0%, 20% { content: ''; }
            40% { content: '.'; }
            60% { content: '..'; }
            80%, 100% { content: '...'; }
        }

        .quick-actions {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
        }

        .quick-action {
            padding: 8px 15px;
            background: rgba(52, 152, 219, 0.1);
            border: 1px solid rgba(52, 152, 219, 0.3);
            border-radius: 20px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            color: #3498db;
        }

        .quick-action:hover {
            background: #3498db;
            color: white;
        }

        @media (max-width: 768px) {
            .container {
                width: 98%;
                height: 95vh;
                flex-direction: column;
            }

            .sidebar {
                width: 100%;
                height: auto;
                min-height: 200px;
            }

            .agent-info {
                padding: 15px;
            }

            .agent-icon {
                width: 50px;
                height: 50px;
                font-size: 20px;
            }

            .feature-icons {
                gap: 20px;
            }

            .feature-icon {
                width: 50px;
                height: 50px;
                font-size: 20px;
            }

            .chat-header h2 {
                font-size: 20px;
            }

            .welcome-message h3 {
                font-size: 24px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="sidebar">
            <div class="sidebar-header">
                <h2><i class="fas fa-robot"></i> 智能体助手</h2>
                <p>选择一个助手开始对话</p>
            </div>
            <div class="agent-info" id="agentInfo">
                <div class="agent-icon" id="agentIcon">
                    <i class="fas fa-brain"></i>
                </div>
                <h3 id="agentName">智能决策助手</h3>
                <p id="agentDescription">自动分析您的需求并选择最合适的专业助手</p>
                <div class="agent-status-info">
                    <span class="status-dot"></span>
                    <span>智能分析中</span>
                </div>
            </div>

            <div class="stats-bar">
                <div class="stat-item">
                    <i class="fas fa-comments"></i>
                    <span id="messageCount">0</span> 条消息
                </div>
                <div class="stat-item">
                    <i class="fas fa-clock"></i>
                    <span id="sessionTime">00:00</span>
                </div>
            </div>
        </div>

        <div class="chat-area">
            <div class="chat-header">
                <h2 id="currentAgent">� 智能决策助手</h2>
                <p id="currentAgentDesc">输入您的需求，我会自动选择最合适的专业助手</p>
                <div class="typing-indicator" id="typingIndicator">
                    <i class="fas fa-circle"></i>
                    <i class="fas fa-circle"></i>
                    <i class="fas fa-circle"></i>
                    智能体正在分析...
                </div>
            </div>

            <div class="chat-messages" id="chatMessages">
                <div class="welcome-message">
                    <h3><i class="fas fa-brain"></i> 智能决策助手</h3>
                    <p>我会自动分析您的需求，并选择最合适的专业助手来帮助您。</p>
                    <p>支持：作文写作、工作报告、公文写作、新媒体文案、小红书内容等</p>
                    <p><strong>直接输入您的需求即可开始！</strong></p>
                    <div class="feature-icons">
                        <div class="feature-icon" title="智能分析">
                            <i class="fas fa-brain"></i>
                        </div>
                        <div class="feature-icon" title="自动选择">
                            <i class="fas fa-magic"></i>
                        </div>
                        <div class="feature-icon" title="专业助手">
                            <i class="fas fa-user-tie"></i>
                        </div>
                    </div>
                </div>
            </div>

            <div class="loading" id="messageLoading">
                <i class="fas fa-brain"></i> 智能体正在思考中<span class="loading-dots"></span>
            </div>

            <div class="chat-input">
                <div class="input-wrapper">
                    <input type="text" id="messageInput" placeholder="请输入您的需求，例如：请帮我写一篇关于春天的作文">
                    <button class="emoji-btn" type="button" title="表情">😊</button>
                </div>
                <button id="sendButton">
                    <i class="fas fa-paper-plane"></i> 发送
                </button>
            </div>
        </div>
    </div>

    <script>
        let messageCount = 0;
        let currentSessionId = 'smart_assistant_' + Date.now(); // 智能助手会话ID

        // 页面加载时初始化
        window.onload = function() {
            setupEventListeners();
            initializeSmartAssistant();
        };

        // 设置事件监听器
        function setupEventListeners() {
            document.getElementById('sendButton').addEventListener('click', sendMessage);
            document.getElementById('messageInput').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    sendMessage();
                }
            });

            // 表情按钮点击事件
            document.querySelector('.emoji-btn').addEventListener('click', function() {
                const input = document.getElementById('messageInput');
                const emojis = ['😊', '👍', '❤️', '😂', '🤔', '👏', '🎉', '💡'];
                const randomEmoji = emojis[Math.floor(Math.random() * emojis.length)];
                input.value += randomEmoji;
                input.focus();
            });
        }

        // 初始化智能助手
        function initializeSmartAssistant() {
            // 添加欢迎消息
            addMessage('您好！我是智能决策助手。请直接告诉我您的需求，我会自动分析并选择最合适的专业助手来帮助您。', 'agent');

            // 聚焦输入框
            document.getElementById('messageInput').focus();

            // 开始计时
            startSessionTimer();
        }

        // 发送消息到智能决策助手
        async function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();

            if (!message) return;

            // 添加用户消息到聊天区域
            addMessage(message, 'user');
            input.value = '';

            // 禁用发送按钮
            document.getElementById('sendButton').disabled = true;
            showTypingIndicator(true);

            try {
                // 调用智能决策助手API
                const response = await fetch('/api/smart_chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        message: message,
                        session_id: currentSessionId
                    })
                });

                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }

                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                let buffer = '';

                while (true) {
                    const { done, value } = await reader.read();

                    if (done) break;

                    buffer += decoder.decode(value, { stream: true });
                    const lines = buffer.split('\n');
                    buffer = lines.pop(); // 保留不完整的行

                    for (const line of lines) {
                        if (line.startsWith('data: ')) {
                            try {
                                const data = JSON.parse(line.slice(6));

                                if (data.type === 'decision') {
                                    // 显示决策信息
                                    addDecisionMessage(data.content);
                                } else if (data.type === 'reasoning') {
                                    // 显示推理过程
                                    addReasoningStep(data.content);
                                } else if (data.type === 'content') {
                                    // 显示最终内容
                                    removeThinkingIndicator();
                                    removeReasoningContainer();
                                    addMessage(data.content, 'agent');
                                } else if (data.type === 'error') {
                                    removeThinkingIndicator();
                                    removeReasoningContainer();
                                    addMessage('抱歉，处理您的请求时出现了错误：' + data.content, 'agent');
                                } else if (data.type === 'end') {
                                    removeThinkingIndicator();
                                    removeReasoningContainer();
                                    break;
                                }
                            } catch (e) {
                                console.error('解析SSE数据失败:', e);
                            }
                        }
                    }
                }

            } catch (error) {
                console.error('发送消息失败:', error);
                removeThinkingIndicator();
                removeReasoningContainer();
                addMessage('网络连接错误，请稍后重试。', 'agent');
            } finally {
                document.getElementById('sendButton').disabled = false;
                showTypingIndicator(false);
                input.focus();
            }
        }

        // 添加决策信息消息
        function addDecisionMessage(content) {
            const chatMessages = document.getElementById('chatMessages');
            const decisionElement = document.createElement('div');
            decisionElement.className = 'thinking-indicator';
            decisionElement.innerHTML = `
                <i class="fas fa-brain"></i>
                <span>${content}</span>
            `;
            chatMessages.appendChild(decisionElement);
            chatMessages.scrollTop = chatMessages.scrollHeight;

            // 3秒后自动移除决策信息
            setTimeout(() => {
                if (decisionElement.parentNode) {
                    decisionElement.remove();
                }
            }, 3000);
        }



        // 添加消息到聊天区域
        function addMessage(content, type) {
            const chatMessages = document.getElementById('chatMessages');
            const messageElement = document.createElement('div');
            messageElement.className = `message ${type}`;

            const avatar = document.createElement('div');
            avatar.className = 'message-avatar';
            avatar.innerHTML = type === 'user' ? '<i class="fas fa-user"></i>' : '<i class="fas fa-robot"></i>';

            const messageContent = document.createElement('div');

            // 处理内容格式
            let processedContent = content;
            if (type === 'agent') {
                // 对于智能体消息，使用Markdown渲染
                try {
                    processedContent = marked.parse(content);
                } catch (error) {
                    console.warn('Markdown parsing failed:', error);
                    // 如果Markdown解析失败，至少处理换行符
                    processedContent = content.replace(/\n/g, '<br>');
                }
            } else {
                // 对于用户消息，只处理换行符
                processedContent = content.replace(/\n/g, '<br>');
            }

            const messageHTML = `
                <div class="message-content">${processedContent}</div>
                <div class="message-time">
                    <i class="fas fa-clock"></i>
                    ${new Date().toLocaleTimeString()}
                </div>
            `;

            messageContent.innerHTML = messageHTML;
            messageElement.appendChild(avatar);
            messageElement.appendChild(messageContent);
            chatMessages.appendChild(messageElement);

            // 滚动到底部
            chatMessages.scrollTop = chatMessages.scrollHeight;

            // 更新消息计数
            messageCount++;
            updateMessageCount(messageCount);

            return messageElement;
        }

        // 添加推理容器
        function addReasoningContainer() {
            const chatMessages = document.getElementById('chatMessages');
            const reasoningContainer = document.createElement('div');
            reasoningContainer.id = 'reasoning-container';
            reasoningContainer.innerHTML = `
                <div class="reasoning-header">
                    <i class="fas fa-brain"></i>
                    <span>AI推理过程</span>
                </div>
                <div class="reasoning-container" id="reasoning-content">
                </div>
            `;
            chatMessages.appendChild(reasoningContainer);

            // 滚动到底部
            chatMessages.scrollTop = chatMessages.scrollHeight;

            return reasoningContainer;
        }

        // 添加推理步骤
        function addReasoningStep(content) {
            let reasoningContent = document.getElementById('reasoning-content');

            // 如果推理容器不存在，创建它
            if (!reasoningContent) {
                addReasoningContainer();
                reasoningContent = document.getElementById('reasoning-content');
            }

            const reasoningStep = document.createElement('div');
            reasoningStep.className = 'reasoning-log';
            reasoningStep.innerHTML = `
                <span>${content}</span>
            `;

            reasoningContent.appendChild(reasoningStep);

            // 滚动到底部
            const chatMessages = document.getElementById('chatMessages');
            chatMessages.scrollTop = chatMessages.scrollHeight;

            // 如果推理步骤太多，移除最早的
            const steps = reasoningContent.querySelectorAll('.reasoning-log');
            if (steps.length > 15) {
                steps[0].remove();
            }
        }

        // 移除推理容器
        function removeReasoningContainer() {
            const container = document.getElementById('reasoning-container');
            if (container) {
                // 添加淡出效果
                container.style.opacity = '0.5';
                setTimeout(() => {
                    container.remove();
                }, 500);
            }
        }

        // 添加确认问题
        function addConfirmationQuestion(question) {
            const chatMessages = document.getElementById('chatMessages');
            const confirmationContainer = document.createElement('div');
            confirmationContainer.id = 'confirmation-container';
            confirmationContainer.className = 'confirmation-container';
            confirmationContainer.innerHTML = `
                <div class="confirmation-header">
                    <i class="fas fa-question-circle"></i>
                    <span>${question}</span>
                </div>
                <div class="confirmation-options" id="confirmation-options"></div>
            `;
            chatMessages.appendChild(confirmationContainer);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        // 添加可点击的确认选项
        function addConfirmationOption(optionData) {
            const confirmationOptions = document.getElementById('confirmation-options');
            if (!confirmationOptions) return;

            const optionElement = document.createElement('button');
            optionElement.className = 'confirmation-option-button';
            optionElement.textContent = optionData.text;
            optionElement.onclick = () => selectConfirmationOption(optionData.id, optionData.text);

            confirmationOptions.appendChild(optionElement);

            // 滚动到底部
            const chatMessages = document.getElementById('chatMessages');
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        // 处理确认选项选择
        async function selectConfirmationOption(optionId, optionText) {
            // 显示用户的选择
            addMessage(`我选择：${optionText}`, 'user');

            // 移除确认容器
            removeConfirmationContainer();

            // 发送选择结果到后端
            const confirmMessage = `CONFIRM_CHOICE:${optionId}|${optionText}`;
            await sendConfirmationChoice(confirmMessage);
        }

        // 发送确认选择到后端
        async function sendConfirmationChoice(confirmMessage) {
            if (!currentAgentId) return;

            try {
                showTypingIndicator(true);

                const response = await fetch('/api/chat/stream', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        agent_id: currentAgentId,
                        message: confirmMessage,
                        session_id: currentSessionId
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const reader = response.body.getReader();
                const decoder = new TextDecoder();

                while (true) {
                    const { done, value } = await reader.read();
                    if (done) break;

                    const chunk = decoder.decode(value);
                    const lines = chunk.split('\n');

                    for (const line of lines) {
                        if (line.startsWith('data: ')) {
                            try {
                                const data = JSON.parse(line.slice(6));

                                if (data.type === 'reasoning') {
                                    addReasoningStep(data.content);
                                } else if (data.type === 'confirmation_question') {
                                    addConfirmationQuestion(data.content);
                                } else if (data.type === 'confirmation_option') {
                                    addConfirmationOption(data.content);
                                } else if (data.type === 'waiting') {
                                    addWaitingMessage(data.content);
                                    return; // 等待下一次选择
                                } else if (data.type === 'content') {
                                    removeThinkingIndicator();
                                    removeReasoningContainer();
                                    addMessage(data.content, 'agent');
                                    return; // 完成
                                } else if (data.type === 'error') {
                                    addMessage('抱歉，处理您的选择时出现了错误：' + data.content, 'agent');
                                    return;
                                }
                            } catch (e) {
                                console.error('解析确认响应失败:', e);
                            }
                        }
                    }
                }

            } catch (error) {
                console.error('发送确认选择失败:', error);
                addMessage('网络连接错误，请稍后重试。', 'agent');
            } finally {
                showTypingIndicator(false);
            }
        }

        // 移除确认容器
        function removeConfirmationContainer() {
            const confirmationContainer = document.getElementById('confirmation-container');
            if (confirmationContainer) {
                confirmationContainer.remove();
            }
        }

        // 添加等待消息
        function addWaitingMessage(content) {
            const chatMessages = document.getElementById('chatMessages');
            const waitingElement = document.createElement('div');
            waitingElement.className = 'message agent waiting-message';
            waitingElement.innerHTML = `
                <div class="message-avatar">
                    <i class="fas fa-robot"></i>
                </div>
                <div class="message-content">
                    <div class="waiting-indicator">
                        <i class="fas fa-hourglass-half fa-spin"></i>
                        ${content}
                    </div>
                    <div class="message-time">
                        <i class="fas fa-clock"></i>
                        ${new Date().toLocaleTimeString()}
                    </div>
                </div>
            `;
            chatMessages.appendChild(waitingElement);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        // 添加思考指示器（简单状态）
        function addThinkingIndicator(content) {
            const chatMessages = document.getElementById('chatMessages');
            const thinkingElement = document.createElement('div');
            thinkingElement.className = 'thinking-indicator';
            thinkingElement.innerHTML = `
                <div class="spinner"></div>
                <span>${content}</span>
            `;
            thinkingElement.id = 'thinking-indicator';
            chatMessages.appendChild(thinkingElement);

            // 滚动到底部
            chatMessages.scrollTop = chatMessages.scrollHeight;

            return thinkingElement;
        }

        // 移除思考指示器
        function removeThinkingIndicator() {
            const indicator = document.getElementById('thinking-indicator');
            if (indicator) {
                indicator.remove();
            }
        }

        // 显示/隐藏加载状态
        function showLoading(show) {
            const loading = document.getElementById('messageLoading');
            if (show) {
                loading.classList.add('show');
            } else {
                loading.classList.remove('show');
            }
        }

        // 显示/隐藏打字指示器
        function showTypingIndicator(show) {
            const indicator = document.getElementById('typingIndicator');
            indicator.style.display = show ? 'block' : 'none';
        }

        // 显示错误消息
        function showError(message) {
            addMessage('系统错误：' + message, 'agent');
        }

        // 会话计时器
        let sessionTimer = null;
        let sessionStartTime = null;

        function startSessionTimer() {
            if (sessionTimer) return; // 已经在计时

            sessionStartTime = Date.now();
            sessionTimer = setInterval(() => {
                const elapsed = Math.floor((Date.now() - sessionStartTime) / 1000);
                const minutes = Math.floor(elapsed / 60);
                const seconds = elapsed % 60;
                document.getElementById('sessionTime').textContent =
                    `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            }, 1000);
        }

        function stopSessionTimer() {
            if (sessionTimer) {
                clearInterval(sessionTimer);
                sessionTimer = null;
                sessionStartTime = null;
                document.getElementById('sessionTime').textContent = '00:00';
            }
        }

        // 更新消息计数
        function updateMessageCount(count) {
            document.getElementById('messageCount').textContent = count;
        }

        // 添加一些交互效果
        document.addEventListener('DOMContentLoaded', function() {
            // 为特性图标添加点击效果
            document.querySelectorAll('.feature-icon').forEach(icon => {
                icon.addEventListener('click', function() {
                    this.style.transform = 'scale(0.9)';
                    setTimeout(() => {
                        this.style.transform = '';
                    }, 150);
                });
            });

            // 添加键盘快捷键
            document.addEventListener('keydown', function(e) {
                // Ctrl + Enter 发送消息
                if (e.ctrlKey && e.key === 'Enter') {
                    sendMessage();
                }

                // ESC 清空输入框
                if (e.key === 'Escape') {
                    document.getElementById('messageInput').value = '';
                }
            });
        });

        // 添加一些实用功能
        function clearChat() {
            document.getElementById('chatMessages').innerHTML = '';
            messageCount = 0;
            updateMessageCount(0);
        }

        function exportChat() {
            const messages = document.querySelectorAll('.message');
            let chatText = `多智能体对话记录 - ${new Date().toLocaleString()}\n\n`;

            messages.forEach(msg => {
                const type = msg.classList.contains('user') ? '用户' : '智能体';
                const content = msg.querySelector('div').textContent;
                const time = msg.querySelector('.message-time').textContent.replace('🕐', '').trim();
                chatText += `[${time}] ${type}: ${content}\n`;
            });

            const blob = new Blob([chatText], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `chat-${new Date().getTime()}.txt`;
            a.click();
            URL.revokeObjectURL(url);
        }
    </script>
</body>
</html>