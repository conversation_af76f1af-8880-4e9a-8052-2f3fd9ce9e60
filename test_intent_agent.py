"""
简化的意图决策测试
"""

def simple_intent_matching(user_input: str):
    """简单的规则匹配"""
    user_input_lower = user_input.lower()
    
    # 定义关键词匹配规则
    rules = [
        (["作文", "写作", "文章", "散文", "记叙文", "议论文", "写一篇"], "composition_agent", "检测到写作相关关键词"),
        (["日报", "今日工作", "当日总结", "今天的工作"], "daily_report_agent", "检测到日报相关关键词"),
        (["周报", "本周工作", "一周总结", "这周的工作"], "week_report_agent", "检测到周报相关关键词"),
        (["月报", "月度总结", "本月工作", "这个月的工作"], "month_report_agent", "检测到月报相关关键词"),
        (["公文", "通知", "公告", "函件", "正式文档"], "official_document_writing_agent", "检测到公文相关关键词"),
        (["小红书文案"], "xiaohongshu_copywriting_agent", "检测到小红书文案关键词"),
        (["小红书创作", "小红书内容"], "xiaohongshu_creation_agent", "检测到小红书创作关键词"),
        (["文案", "广告", "营销", "推广", "微博", "朋友圈"], "new_media_copywriting_agent", "检测到新媒体文案关键词"),
    ]
    
    # 匹配关键词
    for keywords, agent_id, reason in rules:
        if any(keyword in user_input for keyword in keywords):
            return agent_id, reason
    
    # 默认使用通用智能体
    return "my_new_agent", "未匹配到特定类型，使用通用智能体"

def test_intent_matching():
    """测试意图匹配功能"""
    print("=== 意图决策测试 ===\n")
    
    # 测试用例
    test_cases = [
        "请帮我写一篇关于春天的作文",
        "我需要写今天的工作日报",
        "帮我整理一下本周的工作周报",
        "需要写一个月度工作总结",
        "帮我写一个会议通知",
        "我要写一个产品推广文案",
        "帮我写小红书的种草文案",
        "我想创作小红书的内容",
        "帮我分析一下这个问题"
    ]
    
    for i, test_input in enumerate(test_cases, 1):
        print(f"测试 {i}: {test_input}")
        
        # 分析意图
        agent_id, reason = simple_intent_matching(test_input)
        
        print(f"  → 选择智能体: {agent_id}")
        print(f"  → 选择理由: {reason}")
        print()

if __name__ == "__main__":
    test_intent_matching()
