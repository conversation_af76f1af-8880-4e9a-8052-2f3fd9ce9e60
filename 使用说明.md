# 智能决策助手使用说明

## 🎯 系统概述

智能决策助手是一个基于AI的自动意图识别系统，用户只需要输入需求，系统会自动分析意图并选择最合适的专业助手来处理请求。

**核心特点：**
- ✨ 无需手动选择智能体
- 🧠 AI自动意图识别
- 🎯 智能选择专业助手
- 💬 流式对话体验
- 📱 Web界面友好

## 🚀 快速开始

### 1. 一键启动
```bash
python start_smart_assistant.py
```

### 2. 手动启动
```bash
python smart_chat_api.py
```

### 3. 访问系统
打开浏览器访问：http://localhost:5000

## 💡 使用方法

### 基本使用
1. 打开Web界面
2. 直接在输入框中输入您的需求
3. 系统自动分析意图并选择助手
4. 查看处理结果

### 示例输入
- **写作需求**：`请帮我写一篇关于春天的作文`
- **工作报告**：`我需要写今天的工作日报`
- **公文写作**：`帮我写一个会议通知`
- **新媒体文案**：`我要写一个产品推广文案`
- **小红书内容**：`帮我写小红书的种草文案`

## 🎯 支持的智能体

| 智能体 | 触发关键词 | 适用场景 |
|--------|------------|----------|
| 📝 作文助手 | 作文、写作、文章 | 文学创作、学术写作 |
| 📊 日报助手 | 日报、今日工作 | 工作总结、日常记录 |
| 📈 周报助手 | 周报、本周工作 | 周度汇报、工作回顾 |
| 📋 月报助手 | 月报、月度总结 | 月度汇报、阶段总结 |
| 📄 公文助手 | 公文、通知、公告 | 正式文档、官方文件 |
| 📱 文案助手 | 文案、广告、营销 | 社交媒体、推广内容 |
| 🌸 小红书助手 | 小红书文案/创作 | 小红书平台内容 |
| 🔧 通用助手 | 其他需求 | 默认处理各种请求 |

## 🔧 系统架构

```
用户输入 → 意图分析 → 智能体选择 → 专业处理 → 结果输出
    ↓         ↓           ↓           ↓         ↓
  自然语言   AI分析     规则匹配    专业智能体   格式化输出
```

### 核心组件
- **意图决策引擎** (`intent_decision_agent.py`)
- **多智能体管理器** (`multi_agent_manager.py`)
- **Web API服务** (`smart_chat_api.py`)
- **前端界面** (`multi_agent_chat.html`)

## 📋 API接口

### 智能聊天接口
```
POST /api/smart_chat
Content-Type: application/json

{
    "message": "用户输入的内容",
    "session_id": "会话ID（可选）"
}
```

### 简单聊天接口
```
POST /api/chat
Content-Type: application/json

{
    "message": "用户输入的内容",
    "session_id": "会话ID（可选）"
}
```

### 会话管理
```
GET /api/sessions          # 获取所有会话
GET /api/sessions/{id}     # 获取特定会话
GET /api/health           # 健康检查
```

## 🛠️ 安装配置

### 环境要求
- Python 3.7+
- Flask
- Flask-CORS
- agno库
- OpenAI API密钥

### 安装步骤
```bash
# 1. 安装依赖
pip install flask flask-cors agno

# 2. 配置API密钥
export OPENAI_API_KEY="your-api-key"

# 3. 启动系统
python start_smart_assistant.py
```

### 配置文件
确保以下文件存在：
- `multi_agent_manager.py` - 多智能体管理器
- `intent_decision_agent.py` - 意图决策引擎
- `smart_chat_api.py` - API服务
- `multi_agent_chat.html` - Web界面

## 🎮 界面功能

### 主要功能
- **智能输入框** - 支持回车发送、表情插入
- **实时反馈** - 显示意图分析和处理过程
- **消息历史** - 保存对话记录
- **会话统计** - 显示消息数量和时长
- **响应式设计** - 适配各种设备

### 快捷键
- `Enter` - 发送消息
- `Ctrl + Enter` - 强制发送
- `Esc` - 清空输入框

## 🔍 故障排除

### 常见问题

**1. 无法启动服务**
- 检查Python环境
- 确认依赖已安装
- 检查端口5000是否被占用

**2. 意图识别失败**
- 检查OpenAI API密钥
- 确认网络连接正常
- 查看控制台错误信息

**3. 智能体加载失败**
- 检查智能体模块文件
- 确认agno库正确安装
- 查看错误日志

**4. 界面无法访问**
- 确认服务已启动
- 检查防火墙设置
- 尝试使用127.0.0.1:5000

### 调试模式
```bash
# 启用详细日志
python smart_chat_api.py
```

## 📈 扩展开发

### 添加新智能体
1. 在 `multi_agent_manager.py` 中添加配置
2. 创建对应的智能体模块
3. 在 `intent_decision_agent.py` 中添加识别规则

### 自定义意图识别
1. 修改意图分析提示词
2. 添加新的关键词规则
3. 调整匹配逻辑

### 界面定制
1. 修改 `multi_agent_chat.html` 样式
2. 添加新的交互功能
3. 自定义主题和布局

## 📞 技术支持

如果遇到问题，请检查：
1. 系统日志输出
2. 浏览器控制台错误
3. API响应状态
4. 网络连接状态

## 🎉 更新日志

- **v1.0** - 初始版本，支持基本的意图识别和智能体调用
- **v1.1** - 优化Web界面，移除手动选择功能
- **v1.2** - 增加流式响应和实时反馈
