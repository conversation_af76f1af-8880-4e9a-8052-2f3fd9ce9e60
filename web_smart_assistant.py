"""
智能助手Web界面
提供基于Web的智能助手服务
"""

from flask import Flask, render_template, request, jsonify, Response
from intent_decision_agent import process_user_request, process_user_request_simple
import json
import uuid
from datetime import datetime

app = Flask(__name__)

# 存储会话信息
sessions = {}

@app.route('/')
def index():
    """主页"""
    return render_template('index.html')

@app.route('/api/chat', methods=['POST'])
def chat():
    """处理聊天请求"""
    try:
        data = request.get_json()
        user_input = data.get('message', '').strip()
        session_id = data.get('session_id', str(uuid.uuid4()))
        
        if not user_input:
            return jsonify({'error': '请输入有效的内容'}), 400
        
        # 记录会话信息
        if session_id not in sessions:
            sessions[session_id] = {
                'created_at': datetime.now(),
                'messages': []
            }
        
        sessions[session_id]['messages'].append({
            'role': 'user',
            'content': user_input,
            'timestamp': datetime.now()
        })
        
        # 处理请求
        result = process_user_request_simple(user_input, session_id)
        
        sessions[session_id]['messages'].append({
            'role': 'assistant',
            'content': result,
            'timestamp': datetime.now()
        })
        
        return jsonify({
            'response': result,
            'session_id': session_id
        })
        
    except Exception as e:
        return jsonify({'error': f'处理请求时出错: {str(e)}'}), 500

@app.route('/api/chat_stream', methods=['POST'])
def chat_stream():
    """流式聊天响应"""
    try:
        data = request.get_json()
        user_input = data.get('message', '').strip()
        session_id = data.get('session_id', str(uuid.uuid4()))
        
        if not user_input:
            return jsonify({'error': '请输入有效的内容'}), 400
        
        def generate():
            try:
                for response in process_user_request(user_input, session_id):
                    yield f"data: {json.dumps(response, ensure_ascii=False)}\n\n"
                yield f"data: {json.dumps({'type': 'end'}, ensure_ascii=False)}\n\n"
            except Exception as e:
                yield f"data: {json.dumps({'type': 'error', 'content': str(e)}, ensure_ascii=False)}\n\n"
        
        return Response(generate(), mimetype='text/plain')
        
    except Exception as e:
        return jsonify({'error': f'处理请求时出错: {str(e)}'}), 500

@app.route('/api/sessions')
def get_sessions():
    """获取所有会话"""
    session_list = []
    for session_id, session_data in sessions.items():
        session_list.append({
            'session_id': session_id,
            'created_at': session_data['created_at'].isoformat(),
            'message_count': len(session_data['messages'])
        })
    return jsonify(session_list)

@app.route('/api/sessions/<session_id>')
def get_session(session_id):
    """获取特定会话的详细信息"""
    if session_id not in sessions:
        return jsonify({'error': '会话不存在'}), 404
    
    session_data = sessions[session_id]
    return jsonify({
        'session_id': session_id,
        'created_at': session_data['created_at'].isoformat(),
        'messages': [
            {
                'role': msg['role'],
                'content': msg['content'],
                'timestamp': msg['timestamp'].isoformat()
            }
            for msg in session_data['messages']
        ]
    })

# 创建模板文件夹和HTML模板
import os

def create_templates():
    """创建模板文件"""
    if not os.path.exists('templates'):
        os.makedirs('templates')
    
    html_content = '''<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能助手</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .chat-area {
            height: 400px;
            overflow-y: auto;
            padding: 20px;
            border-bottom: 1px solid #eee;
        }
        .message {
            margin-bottom: 15px;
            padding: 10px;
            border-radius: 8px;
        }
        .user-message {
            background: #e3f2fd;
            margin-left: 50px;
        }
        .assistant-message {
            background: #f1f8e9;
            margin-right: 50px;
        }
        .decision-message {
            background: #fff3e0;
            margin-right: 50px;
            font-style: italic;
        }
        .input-area {
            padding: 20px;
            display: flex;
            gap: 10px;
        }
        #messageInput {
            flex: 1;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
        #sendButton {
            padding: 10px 20px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        #sendButton:hover {
            background: #5a6fd8;
        }
        #sendButton:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .loading {
            text-align: center;
            color: #666;
            font-style: italic;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 智能助手</h1>
            <p>我会自动分析您的需求并选择最合适的智能体来帮助您</p>
        </div>
        <div class="chat-area" id="chatArea">
            <div class="message assistant-message">
                <strong>🤖 助手:</strong> 您好！我是智能助手，可以帮您处理各种写作需求：<br>
                📝 作文写作、工作报告、公文写作<br>
                📱 新媒体文案、小红书内容创作<br>
                💼 日报、周报、月报等<br><br>
                请直接告诉我您的需求，我会自动选择最合适的专业助手来帮助您！
            </div>
        </div>
        <div class="input-area">
            <input type="text" id="messageInput" placeholder="请输入您的需求..." onkeypress="handleKeyPress(event)">
            <button id="sendButton" onclick="sendMessage()">发送</button>
        </div>
    </div>

    <script>
        let sessionId = null;

        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        }

        function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            
            if (!message) return;
            
            // 显示用户消息
            addMessage('user', message);
            input.value = '';
            
            // 禁用发送按钮
            const sendButton = document.getElementById('sendButton');
            sendButton.disabled = true;
            sendButton.textContent = '处理中...';
            
            // 发送请求
            fetch('/api/chat', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    message: message,
                    session_id: sessionId
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    addMessage('assistant', '❌ ' + data.error);
                } else {
                    sessionId = data.session_id;
                    addMessage('assistant', data.response);
                }
            })
            .catch(error => {
                addMessage('assistant', '❌ 网络错误: ' + error.message);
            })
            .finally(() => {
                // 恢复发送按钮
                sendButton.disabled = false;
                sendButton.textContent = '发送';
            });
        }

        function addMessage(role, content) {
            const chatArea = document.getElementById('chatArea');
            const messageDiv = document.createElement('div');
            
            if (role === 'user') {
                messageDiv.className = 'message user-message';
                messageDiv.innerHTML = `<strong>👤 您:</strong> ${content}`;
            } else {
                // 检查是否包含决策信息
                if (content.includes('🤖 意图分析') || content.includes('📋 选择智能体')) {
                    messageDiv.className = 'message decision-message';
                } else {
                    messageDiv.className = 'message assistant-message';
                }
                messageDiv.innerHTML = `<strong>🤖 助手:</strong> ${content.replace(/\\n/g, '<br>')}`;
            }
            
            chatArea.appendChild(messageDiv);
            chatArea.scrollTop = chatArea.scrollHeight;
        }
    </script>
</body>
</html>'''
    
    with open('templates/index.html', 'w', encoding='utf-8') as f:
        f.write(html_content)

if __name__ == '__main__':
    # 创建模板文件
    create_templates()
    
    print("🌐 启动Web智能助手...")
    print("📱 访问地址: http://localhost:5000")
    print("🔄 按 Ctrl+C 停止服务")
    
    app.run(debug=True, host='0.0.0.0', port=5000)
