# 智能决策助手系统

## 概述

这是一个基于意图识别的智能决策助手系统，用户只需要输入需求，系统会自动分析用户意图并调用最合适的专业智能体来处理请求。

## 系统架构

```
用户输入 → 意图分析 → 智能体选择 → 专业处理 → 结果输出
```

### 核心组件

1. **意图决策智能体** (`intent_decision_agent.py`)
   - 自动分析用户输入的意图
   - 选择最合适的专业智能体
   - 提供决策理由和过程透明化

2. **多智能体管理器** (`multi_agent_manager.py`)
   - 管理所有专业智能体
   - 提供统一的调用接口
   - 支持持续对话和上下文记忆

3. **智能助手主程序** (`smart_assistant.py`)
   - 命令行交互界面
   - 支持单次请求和持续对话

4. **Web智能助手** (`web_smart_assistant.py`)
   - 基于Flask的Web界面
   - 友好的用户交互体验
   - 支持会话管理

## 支持的智能体类型

| 智能体ID | 名称 | 适用场景 |
|---------|------|----------|
| `composition_agent` | 作文助手 | 写作文、散文、记叙文、议论文等文学创作 |
| `daily_report_agent` | 日报助手 | 日报、工作总结、当日工作记录 |
| `week_report_agent` | 周报助手 | 周报、周工作总结、一周工作回顾 |
| `month_report_agent` | 月报助手 | 月报、月度总结、月度工作汇报 |
| `official_document_writing_agent` | 公文写作助手 | 正式文档、通知、公告、函件等 |
| `new_media_copywriting_agent` | 新媒体文案助手 | 微博、朋友圈、广告文案、营销文案 |
| `xiaohongshu_copywriting_agent` | 小红书文案助手 | 小红书平台的文案创作 |
| `xiaohongshu_creation_agent` | 小红书创作助手 | 小红书内容创作 |
| `my_new_agent` | 通用助手 | 其他自定义需求 |

## 意图识别规则

系统使用两层意图识别机制：

### 1. AI智能分析（主要方式）
- 使用GPT-4模型进行深度语义分析
- 理解用户的真实意图和需求
- 提供详细的选择理由

### 2. 关键词匹配（备选方案）
- 当AI分析失败时的备选机制
- 基于预定义关键词规则
- 确保系统的稳定性

## 使用方法

### 1. 命令行版本

```bash
# 交互式对话
python smart_assistant.py

# 单次请求
python smart_assistant.py "请帮我写一篇关于春天的作文"
```

### 2. Web版本

```bash
# 启动Web服务
python web_smart_assistant.py

# 访问 http://localhost:5000
```

### 3. 编程接口

```python
from intent_decision_agent import process_user_request_simple

# 处理用户请求
result = process_user_request_simple("我需要写今天的工作日报")
print(result)
```

## 使用示例

### 示例1：作文写作
```
用户输入：请帮我写一篇关于春天的作文，500字左右
系统分析：检测到写作相关关键词
选择智能体：作文助手
处理结果：生成符合要求的春天主题作文
```

### 示例2：工作报告
```
用户输入：我需要写今天的工作总结
系统分析：检测到日报相关关键词
选择智能体：日报助手
处理结果：生成专业的工作日报格式
```

### 示例3：新媒体文案
```
用户输入：帮我写一个产品推广的朋友圈文案
系统分析：检测到新媒体文案关键词
选择智能体：新媒体文案助手
处理结果：生成适合朋友圈的推广文案
```

## 系统特点

### 1. 智能化
- 自动意图识别，无需手动选择
- AI驱动的语义理解
- 上下文感知的对话能力

### 2. 专业化
- 每个智能体都专注特定领域
- 针对性的提示词和处理逻辑
- 高质量的专业输出

### 3. 透明化
- 显示决策过程和理由
- 用户可以了解系统的选择逻辑
- 便于调试和优化

### 4. 可扩展
- 易于添加新的智能体
- 灵活的配置管理
- 模块化的架构设计

## 配置要求

### 环境依赖
```
- Python 3.7+
- agno库（智能体框架）
- Flask（Web界面，可选）
- OpenAI API密钥
```

### 安装步骤
```bash
# 1. 安装依赖
pip install agno flask

# 2. 配置API密钥
export OPENAI_API_KEY="your-api-key"

# 3. 运行系统
python smart_assistant.py
```

## 扩展开发

### 添加新智能体
1. 在 `multi_agent_manager.py` 中添加智能体配置
2. 创建对应的智能体模块文件
3. 在 `intent_decision_agent.py` 中添加识别规则

### 自定义意图识别
1. 修改 `IntentDecisionAgent` 类的分析逻辑
2. 添加新的关键词匹配规则
3. 调整AI分析的提示词

## 注意事项

1. **API配置**：确保正确配置OpenAI API密钥
2. **模块依赖**：确保所有智能体模块文件存在
3. **网络连接**：AI分析需要网络连接
4. **资源消耗**：每次意图分析会消耗API调用次数

## 故障排除

### 常见问题
1. **智能体加载失败**：检查模块文件是否存在
2. **意图分析失败**：检查API密钥和网络连接
3. **Web界面无法访问**：检查端口是否被占用

### 调试模式
```python
# 启用详细日志
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 更新日志

- v1.0.0：初始版本，支持基本的意图识别和智能体调用
- 支持9种专业智能体
- 提供命令行和Web两种交互方式
- 实现AI驱动的意图分析机制
