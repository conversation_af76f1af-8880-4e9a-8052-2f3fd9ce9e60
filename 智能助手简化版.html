<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能决策助手 - 简化版</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON><PERSON>, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0 0 10px 0;
            font-size: 2.2em;
        }
        
        .header p {
            margin: 0;
            opacity: 0.9;
            font-size: 1.1em;
        }
        
        .content {
            padding: 30px;
        }
        
        .input-section {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        
        .input-section h3 {
            margin: 0 0 15px 0;
            color: #495057;
        }
        
        .input-group {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
        }
        
        #userInput {
            flex: 1;
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        
        #userInput:focus {
            outline: none;
            border-color: #667eea;
        }
        
        #analyzeBtn {
            padding: 12px 24px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: bold;
            transition: transform 0.2s;
        }
        
        #analyzeBtn:hover {
            transform: translateY(-2px);
        }
        
        #result {
            margin-top: 15px;
            padding: 15px;
            background: white;
            border-radius: 8px;
            border: 1px solid #e9ecef;
            display: none;
        }
        
        .examples {
            margin-top: 20px;
        }
        
        .examples h4 {
            margin: 0 0 15px 0;
            color: #495057;
        }
        
        .example-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }
        
        .example-btn {
            padding: 8px 15px;
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 20px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s;
        }
        
        .example-btn:hover {
            background: #bbdefb;
            transform: translateY(-1px);
        }
        
        .decision-info {
            background: #fff3cd;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 15px;
            border-left: 4px solid #ffc107;
        }
        
        .agent-response {
            background: #d1ecf1;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #17a2b8;
        }
        
        .agents-info {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-top: 20px;
        }
        
        .agents-info h3 {
            margin: 0 0 15px 0;
            color: #495057;
        }
        
        .agents-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        
        .agent-card {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border-left: 3px solid #667eea;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        .agent-card h4 {
            margin: 0 0 8px 0;
            color: #495057;
            font-size: 14px;
        }
        
        .agent-card p {
            margin: 0;
            color: #6c757d;
            font-size: 12px;
            line-height: 1.4;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 智能决策助手</h1>
            <p>输入需求，自动选择最合适的专业助手</p>
        </div>
        
        <div class="content">
            <div class="input-section">
                <h3>💬 请输入您的需求</h3>
                <div class="input-group">
                    <input type="text" id="userInput" placeholder="例如：请帮我写一篇关于春天的作文">
                    <button id="analyzeBtn" onclick="analyzeIntent()">分析意图</button>
                </div>
                <div id="result"></div>
                
                <div class="examples">
                    <h4>💡 快速示例（点击试试）</h4>
                    <div class="example-buttons">
                        <span class="example-btn" onclick="setInput('请帮我写一篇关于春天的作文')">📝 写作文</span>
                        <span class="example-btn" onclick="setInput('我需要写今天的工作日报')">📊 写日报</span>
                        <span class="example-btn" onclick="setInput('帮我整理本周工作周报')">📈 写周报</span>
                        <span class="example-btn" onclick="setInput('需要写一个会议通知')">📄 写公文</span>
                        <span class="example-btn" onclick="setInput('我要写产品推广文案')">📱 写文案</span>
                        <span class="example-btn" onclick="setInput('帮我写小红书种草文案')">🌸 小红书</span>
                    </div>
                </div>
            </div>
            
            <div class="agents-info">
                <h3>🎯 可用的专业助手</h3>
                <div class="agents-grid">
                    <div class="agent-card">
                        <h4>📝 作文助手</h4>
                        <p>专业写作，各类文章创作</p>
                    </div>
                    <div class="agent-card">
                        <h4>📊 日报助手</h4>
                        <p>工作日报，简洁记录</p>
                    </div>
                    <div class="agent-card">
                        <h4>📈 周报助手</h4>
                        <p>周度总结，工作回顾</p>
                    </div>
                    <div class="agent-card">
                        <h4>📋 月报助手</h4>
                        <p>月度汇报，阶段总结</p>
                    </div>
                    <div class="agent-card">
                        <h4>📄 公文助手</h4>
                        <p>正式文档，通知公告</p>
                    </div>
                    <div class="agent-card">
                        <h4>📱 文案助手</h4>
                        <p>新媒体文案，营销推广</p>
                    </div>
                    <div class="agent-card">
                        <h4>🌸 小红书助手</h4>
                        <p>小红书专用内容创作</p>
                    </div>
                    <div class="agent-card">
                        <h4>🔧 通用助手</h4>
                        <p>其他各种需求处理</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // 意图分析规则
        const intentRules = [
            {keywords: ["作文", "写作", "文章", "散文", "记叙文", "议论文", "写一篇"], name: "作文助手", reason: "检测到写作相关关键词"},
            {keywords: ["日报", "今日工作", "当日总结", "今天的工作"], name: "日报助手", reason: "检测到日报相关关键词"},
            {keywords: ["周报", "本周工作", "一周总结", "这周的工作"], name: "周报助手", reason: "检测到周报相关关键词"},
            {keywords: ["月报", "月度总结", "本月工作", "这个月的工作"], name: "月报助手", reason: "检测到月报相关关键词"},
            {keywords: ["公文", "通知", "公告", "函件", "正式文档"], name: "公文写作助手", reason: "检测到公文相关关键词"},
            {keywords: ["小红书文案"], name: "小红书文案助手", reason: "检测到小红书文案关键词"},
            {keywords: ["小红书创作", "小红书内容"], name: "小红书创作助手", reason: "检测到小红书创作关键词"},
            {keywords: ["文案", "广告", "营销", "推广", "微博", "朋友圈"], name: "新媒体文案助手", reason: "检测到新媒体文案关键词"}
        ];
        
        function setInput(text) {
            document.getElementById('userInput').value = text;
            analyzeIntent();
        }
        
        function analyzeIntent() {
            const input = document.getElementById('userInput').value.trim();
            const resultDiv = document.getElementById('result');
            
            if (!input) {
                alert('请输入内容');
                return;
            }
            
            // 分析意图
            let selectedAgent = null;
            for (const rule of intentRules) {
                if (rule.keywords.some(keyword => input.includes(keyword))) {
                    selectedAgent = rule;
                    break;
                }
            }
            
            if (!selectedAgent) {
                selectedAgent = {
                    name: "通用助手",
                    reason: "未匹配到特定类型，使用通用智能体"
                };
            }
            
            // 显示结果
            resultDiv.innerHTML = `
                <div class="decision-info">
                    <strong>🤖 意图分析：</strong>${selectedAgent.reason}<br>
                    <strong>📋 选择助手：</strong>${selectedAgent.name}
                </div>
                <div class="agent-response">
                    <strong>📄 处理结果：</strong>${selectedAgent.name}将为您处理："${input}"
                </div>
            `;
            resultDiv.style.display = 'block';
        }
        
        // 回车键支持
        document.getElementById('userInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                analyzeIntent();
            }
        });
    </script>
</body>
</html>
