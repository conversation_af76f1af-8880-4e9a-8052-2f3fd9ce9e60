"""
意图决策智能体
自动判断用户意图并调用相应的多智能体
"""

from agno.agent import Agent
from multi_agent_manager import get_agent_manager
import re
from typing import Dict, Any, Optional, Tuple

class IntentDecisionAgent:
    """意图决策智能体"""
    
    def __init__(self):
        self.manager = get_agent_manager()
        
        # 创建意图分析智能体
        self.intent_analyzer = Agent(
            name="意图分析助手",
            instructions="""
你是一个专业的意图分析助手，负责分析用户输入的内容，判断用户的真实意图，并选择最合适的智能体来处理用户的请求。

可用的智能体类型：
1. composition_agent - 作文助手：适用于写作文、散文、记叙文、议论文等文学创作
2. daily_report_agent - 日报助手：适用于写日报、工作总结、当日工作记录
3. week_report_agent - 周报助手：适用于写周报、周工作总结、一周工作回顾
4. month_report_agent - 月报助手：适用于写月报、月度总结、月度工作汇报
5. official_document_writing_agent - 公文写作助手：适用于正式文档、通知、公告、函件等
6. new_media_copywriting_agent - 新媒体文案助手：适用于微博、朋友圈、广告文案、营销文案
7. xiaohongshu_copywriting_agent - 小红书文案助手：适用于小红书平台的文案创作
8. xiaohongshu_creation_agent - 小红书创作助手：适用于小红书内容创作
9. my_new_agent - 我的新助手：适用于其他自定义需求

分析规则：
- 如果用户提到"作文"、"写作"、"文章"、"散文"、"记叙文"、"议论文"等，选择 composition_agent
- 如果用户提到"日报"、"今日工作"、"当日总结"等，选择 daily_report_agent
- 如果用户提到"周报"、"本周工作"、"一周总结"等，选择 week_report_agent
- 如果用户提到"月报"、"月度总结"、"本月工作"等，选择 month_report_agent
- 如果用户提到"公文"、"通知"、"公告"、"函件"、"正式文档"等，选择 official_document_writing_agent
- 如果用户提到"文案"、"广告"、"营销"、"推广"、"微博"、"朋友圈"等，选择 new_media_copywriting_agent
- 如果用户明确提到"小红书文案"，选择 xiaohongshu_copywriting_agent
- 如果用户明确提到"小红书创作"或"小红书内容"，选择 xiaohongshu_creation_agent
- 其他情况选择 my_new_agent

请严格按照以下格式回复，不要添加任何其他内容：
AGENT_ID: [选择的智能体ID]
REASON: [选择理由，一句话说明]
""",
            model="gpt-4o"
        )
    
    def analyze_intent(self, user_input: str) -> Tuple[str, str]:
        """分析用户意图，返回(智能体ID, 选择理由)"""
        try:
            # 使用意图分析智能体分析用户输入
            response = self.intent_analyzer.run(f"用户输入：{user_input}")
            
            # 解析响应
            if hasattr(response, 'content'):
                content = response.content
            else:
                content = str(response)
            
            # 提取智能体ID和理由
            agent_id_match = re.search(r'AGENT_ID:\s*(\w+)', content)
            reason_match = re.search(r'REASON:\s*(.+)', content)
            
            if agent_id_match and reason_match:
                agent_id = agent_id_match.group(1).strip()
                reason = reason_match.group(1).strip()
                
                # 验证智能体ID是否有效
                available_agents = self.manager.get_available_agents()
                if agent_id in available_agents:
                    return agent_id, reason
                else:
                    # 如果智能体ID无效，使用默认智能体
                    return "my_new_agent", f"智能体ID无效，使用默认智能体。原因：{reason}"
            else:
                # 如果解析失败，使用简单规则匹配
                return self._simple_intent_matching(user_input)
                
        except Exception as e:
            print(f"意图分析出错: {e}")
            # 如果分析失败，使用简单规则匹配
            return self._simple_intent_matching(user_input)
    
    def _simple_intent_matching(self, user_input: str) -> Tuple[str, str]:
        """简单的规则匹配作为备选方案"""
        user_input_lower = user_input.lower()
        
        # 定义关键词匹配规则
        rules = [
            (["作文", "写作", "文章", "散文", "记叙文", "议论文", "写一篇"], "composition_agent", "检测到写作相关关键词"),
            (["日报", "今日工作", "当日总结", "今天的工作"], "daily_report_agent", "检测到日报相关关键词"),
            (["周报", "本周工作", "一周总结", "这周的工作"], "week_report_agent", "检测到周报相关关键词"),
            (["月报", "月度总结", "本月工作", "这个月的工作"], "month_report_agent", "检测到月报相关关键词"),
            (["公文", "通知", "公告", "函件", "正式文档"], "official_document_writing_agent", "检测到公文相关关键词"),
            (["小红书文案"], "xiaohongshu_copywriting_agent", "检测到小红书文案关键词"),
            (["小红书创作", "小红书内容"], "xiaohongshu_creation_agent", "检测到小红书创作关键词"),
            (["文案", "广告", "营销", "推广", "微博", "朋友圈"], "new_media_copywriting_agent", "检测到新媒体文案关键词"),
        ]
        
        # 匹配关键词
        for keywords, agent_id, reason in rules:
            if any(keyword in user_input for keyword in keywords):
                return agent_id, reason
        
        # 默认使用通用智能体
        return "my_new_agent", "未匹配到特定类型，使用通用智能体"
    
    def process_user_request(self, user_input: str, session_id: str = "default"):
        """处理用户请求，自动选择智能体并返回流式响应"""
        # 分析用户意图
        agent_id, reason = self.analyze_intent(user_input)
        
        # 获取智能体信息
        available_agents = self.manager.get_available_agents()
        agent_info = available_agents.get(agent_id, {})
        agent_name = agent_info.get("name", agent_id)
        
        # 生成决策信息
        yield {"type": "decision", "content": f"🤖 意图分析：{reason}"}
        yield {"type": "decision", "content": f"📋 选择智能体：{agent_name}"}
        yield {"type": "decision", "content": f"🚀 开始处理您的请求..."}
        
        # 调用选定的智能体
        try:
            for response in self.manager.chat_with_agent_stream(agent_id, user_input, session_id):
                yield response
        except Exception as e:
            yield {"type": "error", "content": f"调用智能体时出错: {str(e)}"}
    
    def process_user_request_simple(self, user_input: str, session_id: str = "default") -> str:
        """处理用户请求，返回完整响应（非流式）"""
        # 分析用户意图
        agent_id, reason = self.analyze_intent(user_input)
        
        # 获取智能体信息
        available_agents = self.manager.get_available_agents()
        agent_info = available_agents.get(agent_id, {})
        agent_name = agent_info.get("name", agent_id)
        
        # 生成决策信息
        decision_info = f"🤖 意图分析：{reason}\n📋 选择智能体：{agent_name}\n🚀 处理结果：\n\n"
        
        # 调用选定的智能体
        try:
            response = self.manager.chat_with_agent(agent_id, user_input, session_id)
            return decision_info + response
        except Exception as e:
            return decision_info + f"调用智能体时出错: {str(e)}"


# 创建全局决策智能体实例
decision_agent = IntentDecisionAgent()

def process_user_request(user_input: str, session_id: str = "default"):
    """便捷函数：处理用户请求（流式）"""
    return decision_agent.process_user_request(user_input, session_id)

def process_user_request_simple(user_input: str, session_id: str = "default") -> str:
    """便捷函数：处理用户请求（非流式）"""
    return decision_agent.process_user_request_simple(user_input, session_id)


# 示例使用
if __name__ == "__main__":
    print("=== 意图决策智能体测试 ===\n")
    
    # 测试用例
    test_cases = [
        "请帮我写一篇关于春天的作文",
        "我需要写今天的工作日报",
        "帮我整理一下本周的工作周报",
        "需要写一个月度工作总结",
        "帮我写一个会议通知",
        "我要写一个产品推广文案",
        "帮我写小红书的种草文案",
        "我想创作小红书的内容",
        "帮我分析一下这个问题"
    ]
    
    for i, test_input in enumerate(test_cases, 1):
        print(f"测试 {i}: {test_input}")
        
        # 分析意图
        agent_id, reason = decision_agent.analyze_intent(test_input)
        available_agents = decision_agent.manager.get_available_agents()
        agent_name = available_agents.get(agent_id, {}).get("name", agent_id)
        
        print(f"  → 选择智能体: {agent_name} ({agent_id})")
        print(f"  → 选择理由: {reason}")
        print()
