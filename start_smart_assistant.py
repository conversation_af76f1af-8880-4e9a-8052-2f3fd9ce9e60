"""
智能决策助手启动脚本
一键启动整个系统
"""

import os
import sys
import subprocess
import webbrowser
import time
from threading import Timer

def check_dependencies():
    """检查依赖"""
    required_modules = ['flask', 'flask_cors', 'agno']
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            missing_modules.append(module)
    
    if missing_modules:
        print("❌ 缺少以下依赖模块:")
        for module in missing_modules:
            print(f"   - {module}")
        print("\n请安装缺少的模块:")
        print("pip install flask flask-cors agno")
        return False
    
    return True

def check_files():
    """检查必要文件"""
    required_files = [
        'multi_agent_manager.py',
        'intent_decision_agent.py',
        'multi_agent_chat.html',
        'smart_chat_api.py'
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print("❌ 缺少以下必要文件:")
        for file in missing_files:
            print(f"   - {file}")
        return False
    
    return True

def open_browser_delayed():
    """延迟打开浏览器"""
    time.sleep(3)  # 等待服务器启动
    try:
        webbrowser.open('http://localhost:5000')
        print("🌐 已自动打开浏览器")
    except Exception as e:
        print(f"⚠️  无法自动打开浏览器: {e}")
        print("请手动访问: http://localhost:5000")

def main():
    """主函数"""
    print("🚀 智能决策助手启动器")
    print("=" * 50)
    
    # 检查依赖
    print("🔍 检查依赖模块...")
    if not check_dependencies():
        return
    print("✅ 依赖模块检查通过")
    
    # 检查文件
    print("📁 检查必要文件...")
    if not check_files():
        return
    print("✅ 必要文件检查通过")
    
    # 启动服务器
    print("\n🌟 启动智能决策助手服务...")
    print("📱 服务地址: http://localhost:5000")
    print("🔄 按 Ctrl+C 停止服务")
    print("=" * 50)
    
    # 延迟打开浏览器
    timer = Timer(3.0, open_browser_delayed)
    timer.start()
    
    try:
        # 启动Flask应用
        from smart_chat_api import app
        app.run(debug=False, host='0.0.0.0', port=5000)
    except KeyboardInterrupt:
        print("\n👋 服务已停止")
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")
        print("请检查端口5000是否被占用")

if __name__ == '__main__':
    main()
