<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能决策助手 - 离线版</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            display: flex;
            flex-direction: column;
            height: 90vh;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.2em;
            margin-bottom: 10px;
        }
        
        .header p {
            opacity: 0.9;
            font-size: 1.1em;
        }
        
        .status-bar {
            background: #f8f9fa;
            padding: 10px 25px;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 14px;
            color: #6c757d;
        }
        
        .status-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #28a745;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        
        .chat-area {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        
        .chat-messages {
            flex: 1;
            padding: 25px;
            overflow-y: auto;
            background: #f8f9fa;
        }
        
        .message {
            margin: 15px 0;
            padding: 15px 20px;
            border-radius: 15px;
            max-width: 80%;
            word-wrap: break-word;
            animation: slideIn 0.3s ease-out;
        }
        
        @keyframes slideIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .message.user {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            margin-left: auto;
            text-align: right;
        }
        
        .message.agent {
            background: white;
            color: #2c3e50;
            border: 1px solid #e9ecef;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .decision-message {
            background: #fff3cd;
            color: #856404;
            border-left: 4px solid #ffc107;
            margin: 10px 0;
            padding: 10px 15px;
            border-radius: 5px;
            font-size: 14px;
            animation: fadeIn 0.5s ease-out;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        
        .reasoning-message {
            background: #e3f2fd;
            color: #1976d2;
            border-left: 4px solid #2196f3;
            margin: 5px 0;
            padding: 8px 12px;
            border-radius: 5px;
            font-size: 13px;
            animation: fadeIn 0.5s ease-out;
        }
        
        .message-time {
            font-size: 11px;
            opacity: 0.7;
            margin-top: 8px;
        }
        
        .chat-input {
            padding: 25px;
            background: white;
            border-top: 1px solid #e9ecef;
            display: flex;
            gap: 15px;
            align-items: center;
        }
        
        .input-wrapper {
            flex: 1;
            position: relative;
        }
        
        #messageInput {
            width: 100%;
            padding: 15px 50px 15px 20px;
            border: 2px solid #e9ecef;
            border-radius: 25px;
            outline: none;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        
        #messageInput:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .emoji-btn {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            font-size: 20px;
            cursor: pointer;
            opacity: 0.6;
            transition: opacity 0.3s;
        }
        
        .emoji-btn:hover {
            opacity: 1;
        }
        
        #sendButton {
            padding: 15px 25px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: transform 0.2s;
        }
        
        #sendButton:hover {
            transform: translateY(-2px);
        }
        
        #sendButton:disabled {
            background: #bdc3c7;
            cursor: not-allowed;
            transform: none;
        }
        
        .welcome-message {
            text-align: center;
            padding: 40px 20px;
            color: #6c757d;
        }
        
        .welcome-message h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 24px;
        }
        
        .welcome-message p {
            margin-bottom: 10px;
            line-height: 1.6;
        }
        
        .quick-examples {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            justify-content: center;
            margin-top: 20px;
        }
        
        .example-btn {
            padding: 8px 15px;
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 20px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s;
            color: #1976d2;
        }
        
        .example-btn:hover {
            background: #bbdefb;
            transform: translateY(-2px);
        }
        
        @media (max-width: 768px) {
            .container {
                height: 95vh;
                margin: 10px;
            }
            
            .header h1 {
                font-size: 1.8em;
            }
            
            .quick-examples {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧠 智能决策助手</h1>
            <p>离线版 - 无需网络连接，本地智能分析</p>
        </div>
        
        <div class="status-bar">
            <div class="status-indicator">
                <div class="status-dot"></div>
                <span>离线模式运行中</span>
            </div>
            <div>
                <span id="messageCount">0</span> 条消息 | 
                <span id="sessionTime">00:00</span>
            </div>
        </div>
        
        <div class="chat-area">
            <div class="chat-messages" id="chatMessages">
                <div class="welcome-message">
                    <h3>🎯 智能决策助手</h3>
                    <p>我会自动分析您的需求，并选择最合适的专业助手来帮助您。</p>
                    <p><strong>支持：</strong>作文写作、工作报告、公文写作、新媒体文案、小红书内容等</p>
                    <p><strong>直接输入您的需求即可开始！</strong></p>
                    
                    <div class="quick-examples">
                        <span class="example-btn" onclick="setInput('请帮我写一篇关于春天的作文')">📝 写作文</span>
                        <span class="example-btn" onclick="setInput('我需要写今天的工作日报')">📊 写日报</span>
                        <span class="example-btn" onclick="setInput('帮我写一个会议通知')">📄 写公文</span>
                        <span class="example-btn" onclick="setInput('我要写产品推广文案')">📱 写文案</span>
                        <span class="example-btn" onclick="setInput('帮我写小红书种草文案')">🌸 小红书</span>
                    </div>
                </div>
            </div>
            
            <div class="chat-input">
                <div class="input-wrapper">
                    <input type="text" id="messageInput" placeholder="请输入您的需求，例如：请帮我写一篇关于春天的作文">
                    <button class="emoji-btn" onclick="addEmoji()">😊</button>
                </div>
                <button id="sendButton" onclick="sendMessage()">
                    📤 发送
                </button>
            </div>
        </div>
    </div>
